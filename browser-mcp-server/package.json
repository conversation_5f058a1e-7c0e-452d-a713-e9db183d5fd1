{"name": "browser-mcp-server", "version": "1.0.0", "description": "Browser MCP Server for Web Scraping with Puppeteer", "main": "index.js", "scripts": {"start": "npx @modelcontextprotocol/server-puppeteer", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "browser", "scraping", "puppeteer", "automation"], "author": "Browser MCP Server", "license": "MIT", "dependencies": {"@modelcontextprotocol/server-puppeteer": "^2025.5.12", "puppeteer": "^24.10.1"}}