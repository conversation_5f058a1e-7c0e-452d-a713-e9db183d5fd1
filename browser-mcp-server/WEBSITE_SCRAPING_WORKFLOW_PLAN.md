# 🎯 **FULL WEBSITE SCRAPING WORKFLOW PLAN**

## **📋 WORKFLOW OVERVIEW**
```
User Input (domain.com) → Sitemap Discovery → URL Extraction → 
Batch Scraping → Markdown Conversion → Vector DB Preparation
```

## 🔍 **STAGE 1: URL DISCOVERY & EXTRACTION**

### **1.1 Multiple Discovery Methods**
```python
# Primary methods for finding all pages:
1. Sitemap.xml parsing (fastest, most reliable)
2. Robots.txt analysis 
3. Recursive crawling (backup method)
4. Internal link extraction
5. Search engine site: queries (optional)
```

### **1.2 URL Discovery Strategy**
```python
def discover_all_urls(domain):
    urls = set()
    
    # Method 1: Parse sitemap.xml
    urls.update(parse_sitemap(f"{domain}/sitemap.xml"))
    
    # Method 2: Parse robots.txt for additional sitemaps
    urls.update(parse_robots_txt(f"{domain}/robots.txt"))
    
    # Method 3: Crawl homepage for internal links
    urls.update(crawl_internal_links(domain))
    
    # Method 4: Recursive discovery (limited depth)
    urls.update(recursive_crawl(domain, max_depth=3))
    
    return filter_valid_urls(urls)
```

## 🕷️ **STAGE 2: INTELLIGENT CRAWLING**

### **2.1 URL Filtering & Prioritization**
```python
# Filter out unwanted URLs:
EXCLUDE_PATTERNS = [
    r'/admin/', r'/login/', r'/api/', 
    r'\.pdf$', r'\.jpg$', r'\.png$',
    r'/search\?', r'/tag/', r'/category/',
    r'#', r'\?utm_', r'/feed/'
]

# Prioritize important pages:
PRIORITY_PATTERNS = [
    r'^/$',  # Homepage (highest priority)
    r'/about', r'/services', r'/products',
    r'/contact', r'/blog/', r'/news/'
]
```

### **2.2 Crawling Strategy**
```python
# Respectful crawling approach:
- Rate limiting: 1-2 seconds between requests
- Respect robots.txt
- Handle redirects and errors gracefully
- Detect and avoid infinite loops
- Monitor for rate limiting/blocking
```

## 📄 **STAGE 3: CONTENT EXTRACTION & PROCESSING**

### **3.1 Enhanced Content Extraction**
```python
# Extract structured content:
def extract_page_content(url):
    return {
        'url': url,
        'title': extract_title(),
        'meta_description': extract_meta_description(),
        'headings': extract_headings(),  # H1, H2, H3
        'main_content': extract_main_content(),
        'navigation': extract_navigation(),
        'breadcrumbs': extract_breadcrumbs(),
        'last_modified': extract_last_modified(),
        'word_count': calculate_word_count(),
        'language': detect_language()
    }
```

### **3.2 Content Cleaning for Vector DB**
```python
# Optimize content for AI/vector processing:
- Remove navigation, footers, ads
- Clean HTML artifacts
- Normalize whitespace
- Extract meaningful text blocks
- Preserve heading structure
- Remove duplicate content
```

## 📝 **STAGE 4: MARKDOWN CONVERSION**

### **4.1 Structured Markdown Format**
```markdown
# Page Title

**URL:** https://example.com/page
**Last Updated:** 2025-07-16
**Word Count:** 1,234

## Meta Information
- Description: Page meta description
- Keywords: extracted, keywords, here

## Content

### Main Heading
Main content here with proper formatting...

### Another Section
More content...

## Navigation Context
- Parent: /parent-page
- Children: /child-page-1, /child-page-2
```

### **4.2 Vector DB Optimization**
```python
# Optimize for vector database ingestion:
- Chunk content into optimal sizes (500-1000 tokens)
- Preserve semantic meaning
- Add metadata for filtering
- Include URL hierarchy information
- Maintain cross-references between pages
```

## 🏗️ **IMPLEMENTATION PLAN**

### **Phase 1: URL Discovery Engine**
```python
# Create url_discovery.py
class WebsiteDiscovery:
    def discover_all_urls(self, domain)
    def parse_sitemap(self, sitemap_url)
    def crawl_internal_links(self, url)
    def filter_urls(self, urls)
    def prioritize_urls(self, urls)
```

### **Phase 2: Batch Scraper**
```python
# Create batch_website_scraper.py
class BatchWebsiteScraper:
    def scrape_website_complete(self, domain)
    def scrape_url_batch(self, urls)
    def handle_rate_limiting(self)
    def save_progress(self)  # Resume capability
```

### **Phase 3: Markdown Processor**
```python
# Create markdown_processor.py
class MarkdownProcessor:
    def convert_to_markdown(self, content)
    def chunk_for_vector_db(self, markdown)
    def add_metadata(self, chunks)
    def create_index_file(self, all_pages)
```

### **Phase 4: Vector DB Preparation**
```python
# Create vector_db_prep.py
class VectorDBPrep:
    def prepare_chunks(self, markdown_files)
    def generate_embeddings_metadata(self)
    def create_ingestion_format(self)
    def validate_chunks(self)
```

## 📊 **DETAILED TECHNICAL SPECIFICATIONS**

### **1. URL Discovery Methods**

#### **Method 1: Sitemap.xml Parsing**
```python
def parse_sitemap(sitemap_url):
    """
    Parse XML sitemap and extract all URLs
    Handle: sitemap index files, nested sitemaps, lastmod dates
    """
    try:
        response = requests.get(sitemap_url)
        root = ET.fromstring(response.content)
        
        urls = []
        # Handle sitemap index
        if 'sitemapindex' in root.tag:
            for sitemap in root.findall('.//{*}sitemap'):
                loc = sitemap.find('{*}loc').text
                urls.extend(parse_sitemap(loc))
        else:
            # Handle URL set
            for url in root.findall('.//{*}url'):
                loc = url.find('{*}loc').text
                lastmod = url.find('{*}lastmod')
                priority = url.find('{*}priority')
                
                urls.append({
                    'url': loc,
                    'lastmod': lastmod.text if lastmod else None,
                    'priority': float(priority.text) if priority else 0.5
                })
        
        return urls
    except Exception as e:
        print(f"Sitemap parsing failed: {e}")
        return []
```

#### **Method 2: Recursive Link Discovery**
```python
def discover_internal_links(url, domain, visited=None, max_depth=3):
    """
    Recursively discover internal links
    Respect depth limits and avoid cycles
    """
    if visited is None:
        visited = set()
    
    if url in visited or max_depth <= 0:
        return set()
    
    visited.add(url)
    internal_links = set()
    
    try:
        # Use our existing scraper to get page content
        scraper = AdvancedContentScraper()
        # Extract all links from the page
        links = extract_links_from_page(url)
        
        for link in links:
            if is_internal_link(link, domain):
                internal_links.add(link)
                # Recursive call with reduced depth
                internal_links.update(
                    discover_internal_links(link, domain, visited, max_depth-1)
                )
    
    except Exception as e:
        print(f"Error discovering links from {url}: {e}")
    
    return internal_links
```

### **2. Batch Processing Strategy**

#### **Parallel Processing with Rate Limiting**
```python
import asyncio
import aiohttp
from asyncio import Semaphore

class BatchProcessor:
    def __init__(self, max_concurrent=5, delay_between_requests=2):
        self.semaphore = Semaphore(max_concurrent)
        self.delay = delay_between_requests
    
    async def process_urls_batch(self, urls):
        """Process multiple URLs with concurrency control"""
        tasks = []
        for url in urls:
            task = asyncio.create_task(self.process_single_url(url))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    async def process_single_url(self, url):
        """Process single URL with rate limiting"""
        async with self.semaphore:
            try:
                # Use our existing scraper
                result = await self.scrape_url(url)
                await asyncio.sleep(self.delay)  # Rate limiting
                return result
            except Exception as e:
                return {'url': url, 'error': str(e)}
```

### **3. Markdown Conversion Specifications**

#### **Structured Markdown Template**
```python
MARKDOWN_TEMPLATE = """# {title}

**URL:** {url}
**Scraped:** {scraped_at}
**Word Count:** {word_count}
**Language:** {language}

## Metadata
- **Description:** {meta_description}
- **Keywords:** {keywords}
- **Last Modified:** {last_modified}
- **Page Type:** {page_type}

## Hierarchy
- **Parent:** {parent_url}
- **Children:** {child_urls}
- **Breadcrumb:** {breadcrumb}

## Content

{main_content}

## Technical Details
- **Extraction Method:** {extraction_method}
- **Content Reduction:** {content_reduction}
- **Processing Time:** {processing_time}

---
*Generated by Advanced Website Scraper*
"""
```

### **4. Vector Database Preparation**

#### **Chunking Strategy for AI**
```python
def chunk_content_for_vector_db(markdown_content, chunk_size=800, overlap=100):
    """
    Split content into optimal chunks for vector database
    Preserve semantic meaning and context
    """
    chunks = []
    
    # Split by sections first (preserve heading structure)
    sections = split_by_headings(markdown_content)
    
    for section in sections:
        if len(section) <= chunk_size:
            chunks.append(section)
        else:
            # Split large sections with overlap
            sub_chunks = split_with_overlap(section, chunk_size, overlap)
            chunks.extend(sub_chunks)
    
    # Add metadata to each chunk
    for i, chunk in enumerate(chunks):
        chunks[i] = {
            'content': chunk,
            'chunk_id': f"{url_hash}_{i}",
            'source_url': url,
            'chunk_index': i,
            'total_chunks': len(chunks),
            'word_count': len(chunk.split()),
            'section_type': detect_section_type(chunk)
        }
    
    return chunks
```

## 🚀 **IMPLEMENTATION ROADMAP**

### **Week 1: Foundation**
1. ✅ Extend existing `content_scraper.py` 
2. 🔨 Create `url_discovery.py` - Sitemap parsing and link discovery
3. 🔨 Create `website_crawler.py` - Main orchestration class

### **Week 2: Batch Processing**
1. 🔨 Create `batch_scraper.py` - Parallel processing with rate limiting
2. 🔨 Add progress tracking and resume capability
3. 🔨 Implement error handling and retry logic

### **Week 3: Content Processing**
1. 🔨 Create `markdown_processor.py` - Convert scraped content to markdown
2. 🔨 Implement content chunking for vector databases
3. 🔨 Add metadata extraction and enrichment

### **Week 4: Integration & Testing**
1. 🔨 Create `vector_db_prep.py` - Prepare data for vector ingestion
2. 🔨 Build CLI interface for the complete workflow
3. 🔨 Test on various website types and sizes

## 💡 **USAGE EXAMPLE**

```bash
# Complete website scraping workflow:
python website_scraper.py --domain example.com --output-dir ./scraped_sites/example_com

# What it will do:
1. Discover all URLs from example.com
2. Filter and prioritize URLs  
3. Scrape all pages in batches
4. Convert to structured markdown
5. Prepare chunks for vector database
6. Generate summary report

# Output structure:
scraped_sites/example_com/
├── raw_content/           # Raw JSON files
├── markdown/             # Markdown files  
├── vector_chunks/        # Chunked content for vector DB
├── metadata/            # Site structure and metadata
├── sitemap.json         # Discovered URLs
└── scraping_report.json # Summary statistics
```

## 🎯 **KEY BENEFITS FOR AI AGENT**

1. **Comprehensive Coverage** - Gets ALL pages, not just homepage
2. **Structured Data** - Organized markdown with metadata
3. **Vector DB Ready** - Pre-chunked and optimized for embeddings
4. **Contextual Information** - Preserves site hierarchy and relationships
5. **Quality Content** - Filtered and cleaned for AI consumption
6. **Scalable** - Can handle small blogs to large enterprise sites

---

## 📝 **CURRENT STATUS**
- ✅ Plan documented
- 🔨 **Phase 1 in progress** - URL Discovery Engine
- ⏳ Phase 2 pending - Batch Scraper
- ⏳ Phase 3 pending - Markdown Processor  
- ⏳ Phase 4 pending - Vector DB Preparation
