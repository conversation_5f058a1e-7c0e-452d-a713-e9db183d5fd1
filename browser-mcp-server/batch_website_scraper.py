#!/usr/bin/env python3
"""
🚀 BATCH WEBSITE SCRAPER - Phase 2
Complete workflow: URL Discovery → Batch Scraping → Markdown Conversion
Integrates URL Discovery Engine with Content Scraper for full website processing
"""

import asyncio
import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlparse

from url_discovery import URLDiscoveryEngine
from content_scraper import AdvancedContentScraper

class BatchWebsiteScraper:
    """Complete website scraping workflow with organized output."""
    
    def __init__(self, domain: str, output_dir: str = None):
        self.domain = self.normalize_domain(domain)
        self.base_url = f"https://{self.domain}"
        
        # Setup output directory structure
        if output_dir is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = f"scraped_sites/{self.domain}_{timestamp}"
        
        self.output_dir = Path(output_dir)
        self.setup_output_directories()
        
        # Initialize components
        self.url_discovery = URLDiscoveryEngine(self.domain)
        self.content_scraper = AdvancedContentScraper()
        
        # Processing statistics
        self.stats = {
            'domain': self.domain,
            'started_at': datetime.now().isoformat(),
            'urls_discovered': 0,
            'urls_scraped': 0,
            'urls_failed': 0,
            'markdown_files_created': 0,
            'total_content_length': 0,
            'processing_time': 0,
            'errors': []
        }
    
    def normalize_domain(self, domain: str) -> str:
        """Normalize domain to consistent format."""
        domain = domain.strip().lower()
        domain = re.sub(r'^https?://', '', domain)
        domain = re.sub(r'^www\.', '', domain)
        domain = domain.rstrip('/')
        return domain
    
    def setup_output_directories(self):
        """Create organized directory structure for output."""
        directories = [
            self.output_dir,
            self.output_dir / "raw_content",
            self.output_dir / "cleaned_content", 
            self.output_dir / "markdown",
            self.output_dir / "metadata",
            self.output_dir / "screenshots"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Output directory created: {self.output_dir}")
    
    def url_to_filename(self, url: str) -> str:
        """Convert URL to safe filename."""
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if not path:
            return "homepage"
        
        # Replace special characters with underscores
        filename = re.sub(r'[^\w\-_.]', '_', path)
        filename = re.sub(r'_+', '_', filename)  # Remove multiple underscores
        filename = filename.strip('_')
        
        # Limit length and ensure it's not empty
        if not filename or filename == '_':
            filename = "page"
        
        return filename[:100]  # Limit filename length
    
    def create_markdown_content(self, scrape_result: Dict) -> str:
        """Convert scrape result to structured markdown."""
        url = scrape_result.get('url', '')
        title = self.extract_title_from_content(scrape_result.get('cleaned_content', ''))
        
        # Create structured markdown
        markdown_content = f"""# {title}

**URL:** {url}
**Scraped:** {scrape_result.get('scraped_at', '')}
**Word Count:** {scrape_result.get('cleaned_word_count', 0):,}
**Content Length:** {scrape_result.get('cleaned_content_length', 0):,} characters

## Metadata
- **Extraction Method:** {scrape_result.get('extraction_method', 'Unknown')}
- **Content Reduction:** {scrape_result.get('content_reduction', '0%')}
- **Processing Attempt:** {scrape_result.get('attempt', 1)}
- **Screenshot:** {scrape_result.get('screenshot', 'N/A')}

## Filters Applied
{self.format_filters_list(scrape_result.get('filters_applied', []))}

## Content

{scrape_result.get('cleaned_content', 'No content available')}

---
*Generated by Advanced Website Scraper - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        return markdown_content
    
    def extract_title_from_content(self, content: str) -> str:
        """Extract title from content or generate from URL."""
        if not content:
            return "Untitled Page"
        
        # Try to find first line that looks like a title
        lines = content.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line) < 100 and not line.startswith(('http', 'www')):
                return line
        
        return "Untitled Page"
    
    def format_filters_list(self, filters: List[str]) -> str:
        """Format filters list for markdown."""
        if not filters:
            return "- No filters applied"
        
        formatted = []
        for filter_name in filters:
            readable_name = filter_name.replace('_', ' ').title()
            formatted.append(f"- ✅ {readable_name}")
        
        return '\n'.join(formatted)
    
    async def scrape_single_url(self, url_data: Dict) -> Optional[Dict]:
        """Scrape a single URL and return result."""
        url = url_data['url']
        print(f"🔄 Scraping: {url}")
        
        try:
            # Use the existing content scraper
            result = await self.content_scraper.scrape_website(url, max_retries=2)
            
            if result and result.get('status') == 'success':
                # Add URL discovery metadata
                result['discovery_source'] = url_data.get('source', 'unknown')
                result['calculated_priority'] = url_data.get('calculated_priority', 5)
                result['sitemap_priority'] = url_data.get('priority', 0.5)
                result['last_modified'] = url_data.get('lastmod')
                
                print(f"   ✅ Success: {result.get('cleaned_content_length', 0)} chars")
                return result
            else:
                error_msg = result.get('error', 'Unknown error') if result else 'No result returned'
                print(f"   ❌ Failed: {error_msg}")
                self.stats['errors'].append(f"{url}: {error_msg}")
                return None
                
        except Exception as e:
            error_msg = f"Exception during scraping: {e}"
            print(f"   ❌ Error: {error_msg}")
            self.stats['errors'].append(f"{url}: {error_msg}")
            return None
    
    async def process_discovered_urls(self, discovered_urls: List[Dict]) -> List[Dict]:
        """Process all discovered URLs with rate limiting."""
        print(f"\n🚀 BATCH PROCESSING {len(discovered_urls)} URLS")
        print("=" * 60)
        
        # Start the content scraper once
        if not await self.content_scraper.start_mcp_server():
            raise Exception("Failed to start MCP server for content scraping")
        
        scraped_results = []
        
        try:
            for i, url_data in enumerate(discovered_urls, 1):
                print(f"\n[{i}/{len(discovered_urls)}] ", end="")
                
                # Scrape the URL
                result = await self.scrape_single_url(url_data)
                
                if result:
                    scraped_results.append(result)
                    self.stats['urls_scraped'] += 1
                    self.stats['total_content_length'] += result.get('cleaned_content_length', 0)
                else:
                    self.stats['urls_failed'] += 1
                
                # Rate limiting - wait between requests
                if i < len(discovered_urls):  # Don't wait after last URL
                    await asyncio.sleep(2)
        
        finally:
            # Cleanup scraper
            await self.content_scraper.cleanup()
        
        return scraped_results
    
    def save_results(self, scraped_results: List[Dict]) -> Dict[str, int]:
        """Save all results in organized format."""
        print(f"\n💾 SAVING RESULTS TO ORGANIZED DIRECTORIES")
        print("=" * 60)
        
        saved_counts = {
            'raw_files': 0,
            'cleaned_files': 0,
            'markdown_files': 0,
            'metadata_files': 0
        }
        
        for result in scraped_results:
            url = result['url']
            filename_base = self.url_to_filename(url)
            
            try:
                # Save raw content
                raw_file = self.output_dir / "raw_content" / f"{filename_base}.json"
                raw_data = {
                    'url': url,
                    'scraped_at': result['scraped_at'],
                    'content': result.get('raw_content', ''),
                    'content_length': result.get('raw_content_length', 0),
                    'word_count': result.get('raw_word_count', 0),
                    'extraction_method': result.get('extraction_method'),
                    'screenshot': result.get('screenshot')
                }
                
                with open(raw_file, 'w', encoding='utf-8') as f:
                    json.dump(raw_data, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['raw_files'] += 1
                
                # Save cleaned content
                cleaned_file = self.output_dir / "cleaned_content" / f"{filename_base}.json"
                cleaned_data = {
                    'url': url,
                    'scraped_at': result['scraped_at'],
                    'content': result.get('cleaned_content', ''),
                    'content_length': result.get('cleaned_content_length', 0),
                    'word_count': result.get('cleaned_word_count', 0),
                    'content_reduction': result.get('content_reduction'),
                    'filters_applied': result.get('filters_applied', []),
                    'extraction_method': result.get('extraction_method'),
                    'screenshot': result.get('screenshot')
                }
                
                with open(cleaned_file, 'w', encoding='utf-8') as f:
                    json.dump(cleaned_data, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['cleaned_files'] += 1
                
                # Save markdown
                markdown_file = self.output_dir / "markdown" / f"{filename_base}.md"
                markdown_content = self.create_markdown_content(result)
                
                with open(markdown_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                saved_counts['markdown_files'] += 1
                self.stats['markdown_files_created'] += 1
                
                # Save metadata
                metadata_file = self.output_dir / "metadata" / f"{filename_base}_meta.json"
                metadata = {
                    'url': url,
                    'filename_base': filename_base,
                    'discovery_source': result.get('discovery_source'),
                    'calculated_priority': result.get('calculated_priority'),
                    'sitemap_priority': result.get('sitemap_priority'),
                    'last_modified': result.get('last_modified'),
                    'scraped_at': result['scraped_at'],
                    'processing_stats': {
                        'raw_length': result.get('raw_content_length', 0),
                        'cleaned_length': result.get('cleaned_content_length', 0),
                        'reduction': result.get('content_reduction'),
                        'word_count': result.get('cleaned_word_count', 0)
                    }
                }
                
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['metadata_files'] += 1
                
                print(f"   ✅ Saved: {filename_base} (all formats)")
                
            except Exception as e:
                error_msg = f"Failed to save {url}: {e}"
                print(f"   ❌ {error_msg}")
                self.stats['errors'].append(error_msg)
        
        return saved_counts
    
    def create_summary_report(self, discovery_result: Dict, saved_counts: Dict) -> str:
        """Create comprehensive summary report."""
        self.stats['completed_at'] = datetime.now().isoformat()
        
        # Calculate processing time
        started = datetime.fromisoformat(self.stats['started_at'])
        completed = datetime.fromisoformat(self.stats['completed_at'])
        self.stats['processing_time'] = (completed - started).total_seconds()
        
        report = {
            'domain': self.domain,
            'processing_stats': self.stats,
            'discovery_stats': discovery_result.get('stats', {}),
            'output_stats': saved_counts,
            'output_directory': str(self.output_dir),
            'files_created': {
                'raw_content': saved_counts['raw_files'],
                'cleaned_content': saved_counts['cleaned_files'], 
                'markdown': saved_counts['markdown_files'],
                'metadata': saved_counts['metadata_files']
            }
        }
        
        # Save summary report
        summary_file = self.output_dir / "scraping_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        return str(summary_file)
    
    async def scrape_entire_website(self, max_crawl_depth: int = 2) -> Dict:
        """Main method: Complete website scraping workflow."""
        print(f"🚀 STARTING COMPLETE WEBSITE SCRAPING")
        print(f"🎯 Domain: {self.domain}")
        print(f"📁 Output: {self.output_dir}")
        print("=" * 60)
        
        try:
            # Phase 1: Discover all URLs
            print("\n📍 PHASE 1: URL DISCOVERY")
            discovery_result = self.url_discovery.discover_all_urls(max_crawl_depth)
            discovered_urls = discovery_result['urls']
            
            self.stats['urls_discovered'] = len(discovered_urls)
            
            if not discovered_urls:
                raise Exception("No URLs discovered - cannot proceed with scraping")
            
            # Phase 2: Batch scrape all URLs
            print(f"\n📍 PHASE 2: BATCH SCRAPING")
            scraped_results = await self.process_discovered_urls(discovered_urls)
            
            if not scraped_results:
                raise Exception("No URLs successfully scraped")
            
            # Phase 3: Save organized results
            print(f"\n📍 PHASE 3: SAVING ORGANIZED RESULTS")
            saved_counts = self.save_results(scraped_results)
            
            # Phase 4: Create summary report
            print(f"\n📍 PHASE 4: GENERATING SUMMARY REPORT")
            summary_file = self.create_summary_report(discovery_result, saved_counts)
            
            return {
                'status': 'success',
                'domain': self.domain,
                'output_directory': str(self.output_dir),
                'summary_file': summary_file,
                'stats': self.stats,
                'urls_discovered': len(discovered_urls),
                'urls_scraped': len(scraped_results),
                'files_created': saved_counts
            }
            
        except Exception as e:
            error_msg = f"Workflow failed: {e}"
            print(f"\n❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            
            return {
                'status': 'error',
                'error': error_msg,
                'stats': self.stats
            }

async def main():
    """Test the complete batch website scraper."""
    print("🚀 BATCH WEBSITE SCRAPER - Phase 2 Testing")
    print()
    
    # Get domain from user
    domain = input("📝 Enter domain to scrape completely (e.g., itpyx.pk): ").strip()
    
    if not domain:
        print("❌ Please enter a valid domain!")
        return
    
    try:
        # Create batch scraper
        scraper = BatchWebsiteScraper(domain)
        
        # Run complete workflow
        result = await scraper.scrape_entire_website(max_crawl_depth=2)
        
        # Display results
        print("\n" + "=" * 60)
        print("🎉 BATCH SCRAPING COMPLETED")
        print("=" * 60)
        
        if result['status'] == 'success':
            print(f"✅ SUCCESS!")
            print(f"🌐 Domain: {result['domain']}")
            print(f"📁 Output Directory: {result['output_directory']}")
            print(f"📊 URLs Discovered: {result['urls_discovered']}")
            print(f"📄 URLs Scraped: {result['urls_scraped']}")
            print(f"📝 Markdown Files: {result['files_created']['markdown']}")
            print(f"📋 Summary Report: {result['summary_file']}")
            
            print(f"\n📁 Files Created:")
            for file_type, count in result['files_created'].items():
                print(f"   • {file_type}: {count} files")
            
            print(f"\n💡 Next Steps:")
            print(f"   • Check markdown files in: {result['output_directory']}/markdown/")
            print(f"   • Review summary report: {result['summary_file']}")
            print(f"   • Use markdown files for vector database ingestion")
            
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            if result.get('stats', {}).get('errors'):
                print(f"\n🔍 Errors encountered:")
                for error in result['stats']['errors'][:5]:  # Show first 5 errors
                    print(f"   • {error}")
        
    except KeyboardInterrupt:
        print("\n\n👋 Scraping cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
