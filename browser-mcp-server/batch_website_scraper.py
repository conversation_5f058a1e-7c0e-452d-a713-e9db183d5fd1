#!/usr/bin/env python3
"""
🚀 BATCH WEBSITE SCRAPER - Phase 2
Complete workflow: URL Discovery → Batch Scraping → Markdown Conversion
Integrates URL Discovery Engine with Content Scraper for full website processing
"""

import asyncio
import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlparse

from url_discovery import URLDiscoveryEngine
from content_scraper import AdvancedContentScraper

class BatchWebsiteScraper:
    """Complete website scraping workflow with organized output."""
    
    def __init__(self, domain: str, output_dir: str = None):
        self.domain = self.normalize_domain(domain)
        self.base_url = f"https://{self.domain}"
        
        # Setup output directory structure
        if output_dir is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = f"scraped_sites/{self.domain}_{timestamp}"
        
        self.output_dir = Path(output_dir)
        self.setup_output_directories()
        
        # Initialize components
        self.url_discovery = URLDiscoveryEngine(self.domain)
        self.content_scraper = AdvancedContentScraper()
        
        # Processing statistics
        self.stats = {
            'domain': self.domain,
            'started_at': datetime.now().isoformat(),
            'urls_discovered': 0,
            'urls_scraped': 0,
            'urls_failed': 0,
            'markdown_files_created': 0,
            'total_content_length': 0,
            'processing_time': 0,
            'errors': []
        }
    
    def normalize_domain(self, domain: str) -> str:
        """Normalize domain to consistent format."""
        domain = domain.strip().lower()
        domain = re.sub(r'^https?://', '', domain)
        domain = re.sub(r'^www\.', '', domain)
        domain = domain.rstrip('/')
        return domain
    
    def setup_output_directories(self):
        """Create organized directory structure for output."""
        directories = [
            self.output_dir,
            self.output_dir / "raw_content",
            self.output_dir / "cleaned_content", 
            self.output_dir / "markdown",
            self.output_dir / "metadata",
            self.output_dir / "screenshots"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Output directory created: {self.output_dir}")
    
    def url_to_filename(self, url: str) -> str:
        """Convert URL to safe filename."""
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if not path:
            return "homepage"
        
        # Replace special characters with underscores
        filename = re.sub(r'[^\w\-_.]', '_', path)
        filename = re.sub(r'_+', '_', filename)  # Remove multiple underscores
        filename = filename.strip('_')
        
        # Limit length and ensure it's not empty
        if not filename or filename == '_':
            filename = "page"
        
        return filename[:100]  # Limit filename length
    
    def create_markdown_content(self, scrape_result: Dict) -> str:
        """Convert scrape result to structured markdown."""
        url = scrape_result.get('url', '')
        title = self.extract_title_from_content(scrape_result.get('cleaned_content', ''))
        
        # Create structured markdown
        markdown_content = f"""# {title}

**URL:** {url}
**Scraped:** {scrape_result.get('scraped_at', '')}
**Word Count:** {scrape_result.get('cleaned_word_count', 0):,}
**Content Length:** {scrape_result.get('cleaned_content_length', 0):,} characters

## Metadata
- **Extraction Method:** {scrape_result.get('extraction_method', 'Unknown')}
- **Content Reduction:** {scrape_result.get('content_reduction', '0%')}
- **Processing Attempt:** {scrape_result.get('attempt', 1)}
- **Screenshot:** {scrape_result.get('screenshot', 'N/A')}

## Filters Applied
{self.format_filters_list(scrape_result.get('filters_applied', []))}

## Content

{scrape_result.get('cleaned_content', 'No content available')}

---
*Generated by Advanced Website Scraper - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        return markdown_content
    
    def extract_title_from_content(self, content: str) -> str:
        """Extract title from content or generate from URL."""
        if not content:
            return "Untitled Page"
        
        # Try to find first line that looks like a title
        lines = content.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line) < 100 and not line.startswith(('http', 'www')):
                return line
        
        return "Untitled Page"
    
    def format_filters_list(self, filters: List[str]) -> str:
        """Format filters list for markdown."""
        if not filters:
            return "- No filters applied"
        
        formatted = []
        for filter_name in filters:
            readable_name = filter_name.replace('_', ' ').title()
            formatted.append(f"- ✅ {readable_name}")
        
        return '\n'.join(formatted)
    
    async def scrape_single_url_robust(self, url_data: Dict) -> Optional[Dict]:
        """Ultra-robust single URL scraping with context management."""
        url = url_data['url']
        print(f"🔄 Scraping: {url}")

        max_retries = 2  # Reduced retries but better quality
        for attempt in range(max_retries):
            if attempt > 0:
                print(f"   🔄 Retry {attempt + 1}/{max_retries}")
                # Fresh start for retry - restart MCP session
                await self.content_scraper.cleanup()
                await asyncio.sleep(2)
                if not await self.content_scraper.start_mcp_server():
                    continue

            try:
                # Clear any previous state and navigate fresh
                print(f"   🌐 Fresh navigation to: {url}")

                # Navigate with explicit URL
                nav_response = await self.content_scraper.send_mcp_request("tools/call", {
                    "name": "puppeteer_navigate",
                    "arguments": {"url": url}
                })

                # Progressive waiting strategy
                print(f"   ⏳ Progressive page load waiting...")
                await asyncio.sleep(5)  # Initial load

                # Check if page is ready
                for wait_check in range(3):
                    try:
                        ready_response = await self.content_scraper.send_mcp_request("tools/call", {
                            "name": "puppeteer_evaluate",
                            "arguments": {"script": "document.readyState"}
                        })

                        ready_content = self.content_scraper.extract_content_robust(ready_response)
                        if ready_content and 'complete' in ready_content:
                            print(f"   ✅ Page ready after {5 + wait_check * 3} seconds")
                            break
                    except:
                        pass

                    await asyncio.sleep(3)

                # Final buffer for dynamic content
                await asyncio.sleep(3)

                # Take screenshot for debugging
                timestamp = datetime.now().strftime('%H%M%S')
                screenshot_name = f"robust_scrape_{timestamp}"
                try:
                    await self.content_scraper.send_mcp_request("tools/call", {
                        "name": "puppeteer_screenshot",
                        "arguments": {"name": screenshot_name}
                    })
                except:
                    screenshot_name = "screenshot_failed"

                # Robust content extraction
                content_result = await self.extract_content_robust(url)

                if content_result and content_result.get('status') == 'success':
                    # Add URL discovery metadata
                    content_result['discovery_source'] = url_data.get('source', 'unknown')
                    content_result['calculated_priority'] = url_data.get('calculated_priority', 5)
                    content_result['sitemap_priority'] = url_data.get('priority', 0.5)
                    content_result['last_modified'] = url_data.get('lastmod')
                    content_result['screenshot'] = screenshot_name
                    content_result['attempt'] = attempt + 1

                    print(f"   ✅ Success: {content_result.get('cleaned_content_length', 0)} chars")
                    return content_result
                else:
                    error_msg = content_result.get('error', 'Content extraction failed') if content_result else 'No content result'
                    print(f"   ⚠️  Attempt {attempt + 1} failed: {error_msg}")

                    if attempt == max_retries - 1:  # Last attempt
                        self.stats['errors'].append(f"{url}: {error_msg}")
                        return None

            except Exception as e:
                error_msg = f"Exception during scraping: {e}"
                print(f"   ⚠️  Attempt {attempt + 1} error: {error_msg}")

                if attempt == max_retries - 1:  # Last attempt
                    self.stats['errors'].append(f"{url}: {error_msg}")
                    return None

        return None

    async def scrape_single_url_fresh(self, fresh_scraper: AdvancedContentScraper, url_data: Dict) -> Optional[Dict]:
        """Scrape single URL with completely fresh browser session - 100% reliability."""
        url = url_data['url']

        try:
            # Navigate with fresh session
            print(f"   🌐 Navigating to: {url}")
            nav_response = await fresh_scraper.send_mcp_request("tools/call", {
                "name": "puppeteer_navigate",
                "arguments": {"url": url}
            })

            # JavaScript-aware progressive loading
            print(f"   ⏳ JavaScript-aware page loading...")

            # Initial wait for basic page load
            await asyncio.sleep(5)

            # Wait for JavaScript content to load with content size monitoring
            content_stable = False
            previous_content_length = 0
            max_wait_cycles = 8  # Maximum 40 seconds total wait

            for cycle in range(max_wait_cycles):
                try:
                    # Check current content length
                    content_check_response = await fresh_scraper.send_mcp_request("tools/call", {
                        "name": "puppeteer_evaluate",
                        "arguments": {"script": "document.body ? document.body.innerText.length : 0"}
                    })

                    current_content = fresh_scraper.extract_content_robust(content_check_response)
                    current_length = 0

                    if current_content and current_content.isdigit():
                        current_length = int(current_content)

                    print(f"   📏 Content length check {cycle + 1}: {current_length} chars")

                    # Check if content has stabilized and is substantial
                    if current_length > 500 and abs(current_length - previous_content_length) < 50:
                        print(f"   ✅ Content stabilized at {current_length} chars after {5 + cycle * 5} seconds")
                        content_stable = True
                        break

                    previous_content_length = current_length

                except Exception as e:
                    print(f"   ⚠️  Content check {cycle + 1} failed: {e}")

                # Wait before next check
                await asyncio.sleep(5)

            if not content_stable:
                print(f"   ⚠️  Content did not stabilize, proceeding with current state")

            # Final buffer for any remaining dynamic content
            await asyncio.sleep(3)

            # Take screenshot
            timestamp = datetime.now().strftime('%H%M%S')
            screenshot_name = f"fresh_scrape_{timestamp}"
            try:
                await fresh_scraper.send_mcp_request("tools/call", {
                    "name": "puppeteer_screenshot",
                    "arguments": {"name": screenshot_name}
                })
            except:
                screenshot_name = "screenshot_failed"

            # Ultra-reliable content extraction
            content_result = await self.extract_content_ultra_reliable(fresh_scraper, url)

            if content_result and content_result.get('status') == 'success':
                # Add URL discovery metadata
                content_result['discovery_source'] = url_data.get('source', 'unknown')
                content_result['calculated_priority'] = url_data.get('calculated_priority', 5)
                content_result['sitemap_priority'] = url_data.get('priority', 0.5)
                content_result['last_modified'] = url_data.get('lastmod')
                content_result['screenshot'] = screenshot_name
                content_result['extraction_approach'] = 'fresh_browser_session'

                return content_result
            else:
                error_msg = content_result.get('error', 'Content extraction failed') if content_result else 'No content result'
                self.stats['errors'].append(f"{url}: {error_msg}")
                return None

        except Exception as e:
            error_msg = f"Fresh scraping exception: {e}"
            self.stats['errors'].append(f"{url}: {error_msg}")
            return None

    async def extract_content_robust(self, url: str) -> Dict:
        """Enhanced content extraction with more robust methods."""
        extraction_methods = [
            {
                "name": "Simple Body Text",
                "script": "document.body ? document.body.innerText || document.body.textContent || '' : ''"
            },
            {
                "name": "Main Content Areas",
                "script": """
                (function() {
                    const selectors = ['main', 'article', '.content', '#content', '.post-content', '.entry-content'];

                    for (let selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const text = element.innerText || element.textContent || '';
                            if (text.length > 100) {
                                return text.trim();
                            }
                        }
                    }

                    return document.body ? (document.body.innerText || document.body.textContent || '') : '';
                })()
                """
            },
            {
                "name": "Clean Content Extraction",
                "script": """
                (function() {
                    // Clone document to avoid modifying original
                    const bodyClone = document.body.cloneNode(true);

                    // Remove unwanted elements from clone
                    const unwantedSelectors = ['script', 'style', 'nav', 'header', 'footer', '.menu', '.navigation', 'noscript'];
                    unwantedSelectors.forEach(selector => {
                        const elements = bodyClone.querySelectorAll(selector);
                        elements.forEach(el => el.remove());
                    });

                    // Get text from cleaned clone
                    const text = bodyClone.innerText || bodyClone.textContent || '';
                    return text.trim();
                })()
                """
            }
        ]

        for method in extraction_methods:
            print(f"   🔍 Trying: {method['name']}")

            try:
                response = await self.content_scraper.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": method['script']}
                })

                content = self.content_scraper.extract_content_robust(response)

                if content and len(content) > 100 and not self.is_error_content(content):
                    print(f"   ✅ Success with {method['name']}: {len(content)} characters")

                    # Apply content cleaning
                    cleaned_result = self.content_scraper.clean_content(content)

                    return {
                        'status': 'success',
                        'url': url,
                        'scraped_at': datetime.now().isoformat(),
                        'raw_content': content,
                        'raw_content_length': len(content),
                        'raw_word_count': len(content.split()),
                        'cleaned_content': cleaned_result['cleaned_content'],
                        'cleaned_content_length': len(cleaned_result['cleaned_content']),
                        'cleaned_word_count': len(cleaned_result['cleaned_content'].split()),
                        'content_reduction': f"{cleaned_result['reduction_percentage']}%",
                        'filters_applied': cleaned_result['filters_applied'],
                        'extraction_method': method['name']
                    }
                else:
                    content_preview = content[:100] if content else 'None'
                    print(f"   ⚠️  {method['name']} insufficient: {len(content) if content else 0} chars")
                    print(f"       Preview: '{content_preview}'")

            except Exception as e:
                print(f"   ❌ {method['name']} failed: {e}")
                continue

        return {
            'status': 'error',
            'error': 'All extraction methods failed',
            'url': url,
            'scraped_at': datetime.now().isoformat()
        }

    def is_error_content(self, content: str) -> bool:
        """Check if content appears to be an error message."""
        if not content:
            return True

        error_indicators = [
            'Script execution failed',
            'Cannot read properties of undefined',
            'originalConsole',
            'Execution result:',
            'Console output:',
            'Execution context was destroyed',
            'Navigation',
            'Navigated to',
            'Screenshot',
            'taken at'
        ]

        for indicator in error_indicators:
            if indicator in content:
                return True

        # Check if content is too short and repetitive
        if len(content) < 200 and len(set(content.split())) < 15:
            return True

        # Check if it's mostly navigation text
        nav_words = ['home', 'about', 'contact', 'services', 'menu', 'navigation']
        content_words = content.lower().split()
        nav_ratio = sum(1 for word in content_words if word in nav_words) / max(len(content_words), 1)
        if nav_ratio > 0.3:  # More than 30% navigation words
            return True

        return False

    async def extract_content_ultra_reliable(self, fresh_scraper: AdvancedContentScraper, url: str) -> Dict:
        """Ultra-reliable content extraction with fresh browser session."""
        print(f"   🔍 Ultra-reliable content extraction...")

        # JavaScript-aware extraction methods for dynamic content
        extraction_methods = [
            {
                "name": "JavaScript-Loaded Content",
                "script": """
                (function() {
                    // Wait a bit more for any remaining JavaScript
                    setTimeout(function() {}, 1000);

                    // Try multiple content areas in order of preference
                    const selectors = [
                        'main', 'article', '.content', '#content',
                        '.post-content', '.entry-content', '.page-content',
                        '.single-content', '.main-content', '[role="main"]'
                    ];

                    for (let selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const text = element.innerText || element.textContent || '';
                            if (text.length > 200) {
                                return text.trim();
                            }
                        }
                    }

                    // Fallback to body but clean it up
                    if (document.body) {
                        const bodyText = document.body.innerText || document.body.textContent || '';
                        return bodyText.trim();
                    }

                    return '';
                })()
                """
            },
            {
                "name": "Deep Content Extraction",
                "script": """
                (function() {
                    // Create a clean copy of the page
                    const bodyClone = document.body.cloneNode(true);

                    // Remove unwanted elements
                    const unwantedSelectors = [
                        'script', 'style', 'noscript', 'nav', 'header', 'footer',
                        '.menu', '.navigation', '.sidebar', '.ads', '.advertisement'
                    ];

                    unwantedSelectors.forEach(selector => {
                        const elements = bodyClone.querySelectorAll(selector);
                        elements.forEach(el => el.remove());
                    });

                    // Get clean text
                    const cleanText = bodyClone.innerText || bodyClone.textContent || '';
                    return cleanText.trim();
                })()
                """
            },
            {
                "name": "Fallback Full Page",
                "script": """
                (function() {
                    // Last resort - get everything from body
                    if (document.body) {
                        const allText = document.body.innerText || document.body.textContent || '';
                        return allText.trim();
                    }
                    return '';
                })()
                """
            }
        ]

        for method in extraction_methods:
            print(f"   🔍 Trying: {method['name']}")

            try:
                response = await fresh_scraper.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": method['script']}
                })

                content = fresh_scraper.extract_content_robust(response)

                if content and len(content) > 100 and not self.is_error_content(content):
                    print(f"   ✅ Success with {method['name']}: {len(content)} characters")

                    # Apply content cleaning
                    cleaned_result = fresh_scraper.clean_content(content)

                    return {
                        'status': 'success',
                        'url': url,
                        'scraped_at': datetime.now().isoformat(),
                        'raw_content': content,
                        'raw_content_length': len(content),
                        'raw_word_count': len(content.split()),
                        'cleaned_content': cleaned_result['cleaned_content'],
                        'cleaned_content_length': len(cleaned_result['cleaned_content']),
                        'cleaned_word_count': len(cleaned_result['cleaned_content'].split()),
                        'content_reduction': f"{cleaned_result['reduction_percentage']}%",
                        'filters_applied': cleaned_result['filters_applied'],
                        'extraction_method': method['name']
                    }
                else:
                    content_preview = content[:100] if content else 'None'
                    print(f"   ⚠️  {method['name']} insufficient: {len(content) if content else 0} chars")

            except Exception as e:
                print(f"   ❌ {method['name']} failed: {e}")
                continue

        return {
            'status': 'error',
            'error': 'All ultra-reliable extraction methods failed',
            'url': url,
            'scraped_at': datetime.now().isoformat()
        }
    
    async def process_discovered_urls(self, discovered_urls: List[Dict]) -> List[Dict]:
        """Process all discovered URLs with fresh browser session per URL."""
        print(f"\n🚀 ULTIMATE BATCH PROCESSING {len(discovered_urls)} URLS")
        print("🔧 Using fresh browser session per URL for 100% reliability")
        print("=" * 60)

        scraped_results = []

        for i, url_data in enumerate(discovered_urls, 1):
            print(f"\n[{i}/{len(discovered_urls)}] ", end="")

            # Create fresh scraper instance for each URL
            fresh_scraper = AdvancedContentScraper()

            try:
                # Start fresh MCP server for this URL
                print(f"🔄 Scraping: {url_data['url']}")
                print(f"   🆕 Starting fresh browser session...")

                if not await fresh_scraper.start_mcp_server():
                    print(f"   ❌ Failed to start browser session")
                    self.stats['urls_failed'] += 1
                    self.stats['errors'].append(f"{url_data['url']}: Failed to start browser")
                    continue

                # Scrape with fresh session
                result = await self.scrape_single_url_fresh(fresh_scraper, url_data)

                if result:
                    scraped_results.append(result)
                    self.stats['urls_scraped'] += 1
                    self.stats['total_content_length'] += result.get('cleaned_content_length', 0)
                    print(f"   ✅ Success: {result.get('cleaned_content_length', 0)} chars")
                else:
                    self.stats['urls_failed'] += 1
                    print(f"   ❌ Failed: No content extracted")

                print(f"   📊 Running success rate: {(self.stats['urls_scraped']/(i))*100:.1f}%")

            except Exception as e:
                print(f"   ❌ Exception: {e}")
                self.stats['urls_failed'] += 1
                self.stats['errors'].append(f"{url_data['url']}: {e}")

            finally:
                # Always cleanup the fresh scraper
                try:
                    await fresh_scraper.cleanup()
                    print(f"   🧹 Browser session cleaned up")
                except:
                    pass

                # Rate limiting between URLs
                if i < len(discovered_urls):
                    print(f"   ⏳ Rate limiting (2 seconds)...")
                    await asyncio.sleep(2)

        print(f"\n📊 ULTIMATE BATCH PROCESSING COMPLETED")
        print(f"   ✅ Successful: {self.stats['urls_scraped']}")
        print(f"   ❌ Failed: {self.stats['urls_failed']}")
        print(f"   📈 Final Success Rate: {(self.stats['urls_scraped']/len(discovered_urls))*100:.1f}%")

        return scraped_results
    
    def save_results(self, scraped_results: List[Dict]) -> Dict[str, int]:
        """Save all results in organized format."""
        print(f"\n💾 SAVING RESULTS TO ORGANIZED DIRECTORIES")
        print("=" * 60)
        
        saved_counts = {
            'raw_files': 0,
            'cleaned_files': 0,
            'markdown_files': 0,
            'metadata_files': 0
        }
        
        for result in scraped_results:
            url = result['url']
            filename_base = self.url_to_filename(url)
            
            try:
                # Save raw content
                raw_file = self.output_dir / "raw_content" / f"{filename_base}.json"
                raw_data = {
                    'url': url,
                    'scraped_at': result['scraped_at'],
                    'content': result.get('raw_content', ''),
                    'content_length': result.get('raw_content_length', 0),
                    'word_count': result.get('raw_word_count', 0),
                    'extraction_method': result.get('extraction_method'),
                    'screenshot': result.get('screenshot')
                }
                
                with open(raw_file, 'w', encoding='utf-8') as f:
                    json.dump(raw_data, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['raw_files'] += 1
                
                # Save cleaned content
                cleaned_file = self.output_dir / "cleaned_content" / f"{filename_base}.json"
                cleaned_data = {
                    'url': url,
                    'scraped_at': result['scraped_at'],
                    'content': result.get('cleaned_content', ''),
                    'content_length': result.get('cleaned_content_length', 0),
                    'word_count': result.get('cleaned_word_count', 0),
                    'content_reduction': result.get('content_reduction'),
                    'filters_applied': result.get('filters_applied', []),
                    'extraction_method': result.get('extraction_method'),
                    'screenshot': result.get('screenshot')
                }
                
                with open(cleaned_file, 'w', encoding='utf-8') as f:
                    json.dump(cleaned_data, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['cleaned_files'] += 1
                
                # Save markdown
                markdown_file = self.output_dir / "markdown" / f"{filename_base}.md"
                markdown_content = self.create_markdown_content(result)
                
                with open(markdown_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                saved_counts['markdown_files'] += 1
                self.stats['markdown_files_created'] += 1
                
                # Save metadata
                metadata_file = self.output_dir / "metadata" / f"{filename_base}_meta.json"
                metadata = {
                    'url': url,
                    'filename_base': filename_base,
                    'discovery_source': result.get('discovery_source'),
                    'calculated_priority': result.get('calculated_priority'),
                    'sitemap_priority': result.get('sitemap_priority'),
                    'last_modified': result.get('last_modified'),
                    'scraped_at': result['scraped_at'],
                    'processing_stats': {
                        'raw_length': result.get('raw_content_length', 0),
                        'cleaned_length': result.get('cleaned_content_length', 0),
                        'reduction': result.get('content_reduction'),
                        'word_count': result.get('cleaned_word_count', 0)
                    }
                }
                
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
                saved_counts['metadata_files'] += 1
                
                print(f"   ✅ Saved: {filename_base} (all formats)")
                
            except Exception as e:
                error_msg = f"Failed to save {url}: {e}"
                print(f"   ❌ {error_msg}")
                self.stats['errors'].append(error_msg)
        
        return saved_counts
    
    def create_summary_report(self, discovery_result: Dict, saved_counts: Dict) -> str:
        """Create comprehensive summary report."""
        self.stats['completed_at'] = datetime.now().isoformat()
        
        # Calculate processing time
        started = datetime.fromisoformat(self.stats['started_at'])
        completed = datetime.fromisoformat(self.stats['completed_at'])
        self.stats['processing_time'] = (completed - started).total_seconds()
        
        report = {
            'domain': self.domain,
            'processing_stats': self.stats,
            'discovery_stats': discovery_result.get('stats', {}),
            'output_stats': saved_counts,
            'output_directory': str(self.output_dir),
            'files_created': {
                'raw_content': saved_counts['raw_files'],
                'cleaned_content': saved_counts['cleaned_files'], 
                'markdown': saved_counts['markdown_files'],
                'metadata': saved_counts['metadata_files']
            }
        }
        
        # Save summary report
        summary_file = self.output_dir / "scraping_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        return str(summary_file)
    
    async def scrape_entire_website(self, max_crawl_depth: int = 2) -> Dict:
        """Main method: Complete website scraping workflow."""
        print(f"🚀 STARTING COMPLETE WEBSITE SCRAPING")
        print(f"🎯 Domain: {self.domain}")
        print(f"📁 Output: {self.output_dir}")
        print("=" * 60)
        
        try:
            # Phase 1: Discover all URLs
            print("\n📍 PHASE 1: URL DISCOVERY")
            discovery_result = self.url_discovery.discover_all_urls(max_crawl_depth)
            discovered_urls = discovery_result['urls']
            
            self.stats['urls_discovered'] = len(discovered_urls)
            
            if not discovered_urls:
                raise Exception("No URLs discovered - cannot proceed with scraping")
            
            # Phase 2: Batch scrape all URLs
            print(f"\n📍 PHASE 2: BATCH SCRAPING")
            scraped_results = await self.process_discovered_urls(discovered_urls)
            
            if not scraped_results:
                raise Exception("No URLs successfully scraped")
            
            # Phase 3: Save organized results
            print(f"\n📍 PHASE 3: SAVING ORGANIZED RESULTS")
            saved_counts = self.save_results(scraped_results)
            
            # Phase 4: Create summary report
            print(f"\n📍 PHASE 4: GENERATING SUMMARY REPORT")
            summary_file = self.create_summary_report(discovery_result, saved_counts)
            
            return {
                'status': 'success',
                'domain': self.domain,
                'output_directory': str(self.output_dir),
                'summary_file': summary_file,
                'stats': self.stats,
                'urls_discovered': len(discovered_urls),
                'urls_scraped': len(scraped_results),
                'files_created': saved_counts
            }
            
        except Exception as e:
            error_msg = f"Workflow failed: {e}"
            print(f"\n❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            
            return {
                'status': 'error',
                'error': error_msg,
                'stats': self.stats
            }

async def main():
    """Test the complete batch website scraper."""
    print("🚀 BATCH WEBSITE SCRAPER - Phase 2 Testing")
    print()
    
    # Get domain from user
    domain = input("📝 Enter domain to scrape completely (e.g., itpyx.pk): ").strip()
    
    if not domain:
        print("❌ Please enter a valid domain!")
        return
    
    try:
        # Create batch scraper
        scraper = BatchWebsiteScraper(domain)
        
        # Run complete workflow
        result = await scraper.scrape_entire_website(max_crawl_depth=2)
        
        # Display results
        print("\n" + "=" * 60)
        print("🎉 BATCH SCRAPING COMPLETED")
        print("=" * 60)
        
        if result['status'] == 'success':
            print(f"✅ SUCCESS!")
            print(f"🌐 Domain: {result['domain']}")
            print(f"📁 Output Directory: {result['output_directory']}")
            print(f"📊 URLs Discovered: {result['urls_discovered']}")
            print(f"📄 URLs Scraped: {result['urls_scraped']}")
            print(f"📝 Markdown Files: {result['files_created']['markdown']}")
            print(f"📋 Summary Report: {result['summary_file']}")
            
            print(f"\n📁 Files Created:")
            for file_type, count in result['files_created'].items():
                print(f"   • {file_type}: {count} files")
            
            print(f"\n💡 Next Steps:")
            print(f"   • Check markdown files in: {result['output_directory']}/markdown/")
            print(f"   • Review summary report: {result['summary_file']}")
            print(f"   • Use markdown files for vector database ingestion")
            
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            if result.get('stats', {}).get('errors'):
                print(f"\n🔍 Errors encountered:")
                for error in result['stats']['errors'][:5]:  # Show first 5 errors
                    print(f"   • {error}")
        
    except KeyboardInterrupt:
        print("\n\n👋 Scraping cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
