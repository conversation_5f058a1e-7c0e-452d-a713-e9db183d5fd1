# 🤖 Browser MCP Server for Web Scraping

A standalone Browser MCP (Model Context Protocol) server powered by Puppeteer and Gemini Vision AI for intelligent web scraping and automation tasks.

## ✨ Features

### 🔧 **Core Capabilities**
- **Intelligent Browser Automation**: AI-powered website navigation and interaction
- **Visual Understanding**: Screenshot-based content analysis with Gemini Vision
- **Anti-Detection**: Advanced techniques to avoid bot detection
- **Dynamic Content Support**: Handles JavaScript-heavy and modern websites
- **Flexible Configuration**: Customizable for various scraping scenarios

### 🎯 **Web Scraping Specializations**
- **Contact Information Extraction**: Email, phone, and contact person details
- **Business Data Mining**: Company information, descriptions, and profiles
- **Multi-Source Aggregation**: LinkedIn, directories, company websites
- **Visual Verification**: Screenshot evidence of extracted data
- **Quality Scoring**: Intelligent data ranking and validation

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or copy the browser-mcp-server folder
cd browser-mcp-server

# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies (MCP Puppeteer server)
npm install
```

### 2. Configuration

```bash
# Copy configuration templates
cp config/mcp_agent.secrets.yaml.example config/mcp_agent.secrets.yaml

# Edit config/mcp_agent.secrets.yaml with your API keys
# Edit config/mcp_agent.config.yaml for browser settings
# Edit config/scraping_config.json for target customization
```

### 3. Basic Usage

```python
from src.agents.mcp_browser_agent import MCPLeadGenerationAgent

# Initialize the agent
agent = MCPLeadGenerationAgent(
    api_key="your-openrouter-api-key",
    model="gemini-2.0-flash"
)

# Run web scraping
await agent.initialize()
results = await agent.run_lead_generation_campaign(
    queries=["tech startups in San Francisco"],
    max_leads_per_query=5
)
await agent.cleanup()
```

## 📁 Project Structure

```
browser-mcp-server/
├── 📋 README.md                    # This file
├── 📋 requirements.txt             # Python dependencies
├── 📋 package.json                # Node.js dependencies (MCP server)
├── 🔧 config/                     # Configuration files
│   ├── mcp_agent.config.yaml      # MCP browser configuration
│   ├── mcp_agent.secrets.yaml.example # API keys template
│   └── scraping_config.json       # Scraping targets and settings
├── 🎯 src/                        # Source code
│   ├── agents/                    # Browser automation agents
│   │   ├── mcp_browser_agent.py   # Main MCP browser agent
│   │   └── gemini_llm_wrapper.py  # Gemini Vision API wrapper
│   └── utils/                     # Utility functions
│       ├── detailed_logger.py     # Advanced logging system
│       ├── api_key_manager.py     # API key management
│       └── asyncio_cleanup.py     # Async cleanup utilities
├── 📸 screenshots/                # Screenshot storage
├── 📊 logs/                       # Log files
└── 🧪 examples/                   # Usage examples
```

## 🔧 Configuration

### API Keys Required

1. **OpenRouter API Key**: For Gemini Vision model access
2. **Alternative**: Direct Google Gemini API key

### Configuration Files

- `config/mcp_agent.config.yaml`: MCP server and browser settings
- `config/mcp_agent.secrets.yaml`: API keys and secrets
- `config/scraping_config.json`: Target sites, extraction rules, and quality settings

## 🎯 Usage Examples

### Basic Web Scraping
```python
import asyncio
from src.agents.mcp_browser_agent import run_mcp_lead_generation

async def main():
    results = await run_mcp_lead_generation(
        queries=["AI startups", "SaaS companies"],
        api_key="your-api-key",
        max_leads_per_query=3
    )
    print(f"Found {len(results)} results")

asyncio.run(main())
```

### Advanced Configuration
```python
from src.agents.mcp_browser_agent import MCPLeadGenerationAgent

agent = MCPLeadGenerationAgent(
    api_key="your-api-key",
    model="gemini-2.0-flash",
    agent_config={
        "browser_timeout": 60,
        "take_screenshots": True,
        "verify_contact_info": True,
        "target_sites": ["linkedin.com", "crunchbase.com"]
    }
)
```

### Custom Scraping Targets
```python
# Edit config/scraping_config.json to customize:
{
    "target_sites": [
        "your-target-site.com",
        "another-directory.com"
    ],
    "extraction_rules": {
        "email_selectors": [".contact-email", "[href^='mailto:']"],
        "phone_selectors": [".phone", ".contact-phone"]
    }
}
```

## 🧪 Testing

```bash
# Run basic functionality test
python examples/test_basic_scraping.py

# Test specific website
python examples/test_website_scraping.py --url "https://example.com"

# Test with screenshots
python examples/test_visual_verification.py
```

## 📊 Output Format

Results are returned as structured data:

```json
{
    "company_name": "Example Corp",
    "extracted_email": "<EMAIL>",
    "extracted_phone": "******-0123",
    "contact_quality": "HIGH",
    "source_url": "https://example.com/contact",
    "screenshot_path": "screenshots/example_corp_contact.png",
    "extraction_timestamp": "2024-01-15T10:30:00Z",
    "verification_status": "verified"
}
```

## 🔧 Customization

### Adding New Target Sites
1. Edit `config/scraping_config.json`
2. Add site-specific extraction rules
3. Configure quality scoring weights

### Custom Extraction Logic
1. Modify `src/agents/mcp_browser_agent.py`
2. Add new parsing methods
3. Update the system prompt for AI guidance

### Browser Settings
1. Edit `config/mcp_agent.config.yaml`
2. Adjust Puppeteer launch options
3. Configure timeouts and delays

## 🚨 Best Practices

### Ethical Scraping
- Respect robots.txt files
- Implement reasonable delays between requests
- Don't overload target servers
- Follow website terms of service

### Performance Optimization
- Use headless mode for production
- Implement proper error handling
- Cache results when appropriate
- Monitor resource usage

### Security
- Keep API keys secure
- Use environment variables
- Regularly update dependencies
- Monitor for security vulnerabilities

## 🆘 Troubleshooting

### Common Issues

1. **MCP Server Not Starting**
   ```bash
   # Check Node.js installation
   node --version
   npm --version
   
   # Reinstall dependencies
   npm install
   ```

2. **Browser Launch Failures**
   ```bash
   # Install required system dependencies (Linux)
   sudo apt-get install -y chromium-browser
   
   # Or use bundled Chromium
   npm install puppeteer
   ```

3. **API Key Issues**
   ```bash
   # Verify API key format
   # Check config/mcp_agent.secrets.yaml
   # Test with minimal example
   ```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 🎯 Roadmap

- [ ] Support for more browser engines (Firefox, Safari)
- [ ] Advanced anti-detection techniques
- [ ] Proxy rotation and IP management
- [ ] Distributed scraping across multiple instances
- [ ] Real-time monitoring dashboard
- [ ] Integration with popular scraping frameworks