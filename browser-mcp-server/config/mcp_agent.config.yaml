execution_engine: asyncio
logger:
  transports: [console, file]
  level: debug
  progress_display: true
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp" # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    puppeteer:
      command: "xvfb-run"
      args: ["-a", "--server-args=-screen 0 1024x768x24 -ac +extension GLX +render -noreset", "npx", "-y", "@modelcontextprotocol/server-puppeteer"]
      env:
        PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
        PUPPETEER_EXECUTABLE_PATH: "/usr/bin/google-chrome-stable"
        DISPLAY: ":99"
        DISPLAY: ":99"
      launch_options:
        headless: true
        executablePath: "/usr/bin/google-chrome-stable"
        args: ["--no-sandbox", "--disable-setuid-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--no-first-run", "--disable-extensions", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--virtual-time-budget=5000", "--display=:99"]


openai:
  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored
  default_model: "gemini-2.5-flash"
