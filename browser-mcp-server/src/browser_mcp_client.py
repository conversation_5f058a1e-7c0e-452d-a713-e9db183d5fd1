#!/usr/bin/env python3
"""
Pure Browser MCP Client
Direct MCP client for browser automation without lead generation
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# MCP imports - use the working approach
import subprocess
import tempfile

# Import MCP client components
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters

class BrowserMCPClient:
    """Pure browser MCP client for web scraping."""
    
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.server_params = None
        
    async def initialize(self, timeout: int = 60):
        """Initialize the MCP client connection with extended timeout handling."""
        print("🔧 Initializing Browser MCP Client...")
        
        try:
            # Configure MCP server parameters
            self.server_params = StdioServerParameters(
                command="xvfb-run",
                args=[
                    "-a",
                    "--server-args=-screen 0 1024x768x24 -ac +extension GLX +render -noreset",
                    "npx",
                    "-y",
                    "@modelcontextprotocol/server-puppeteer"
                ],
                env={
                    "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "true",
                    "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/google-chrome-stable",
                    "DISPLAY": ":99"
                }
            )
            
            print("📡 Starting MCP server connection...")
            
            # Create MCP client session with timeout
            from mcp.client.stdio import stdio_client
            
            # Use timeout for the entire initialization process
            async def _initialize_with_timeout():
                try:
                    print("📡 Creating stdio client context...")
                    self.stdio_context = stdio_client(self.server_params)

                    print("🔌 Establishing stdio connection...")
                    self.read_stream, self.write_stream = await self.stdio_context.__aenter__()
                    print("✅ Stdio connection established")

                    print("🤝 Creating client session...")
                    self.session = ClientSession(self.read_stream, self.write_stream)
                    print("✅ Client session created")

                    print("⚡ Initializing session...")
                    await self.session.initialize()
                    print("✅ Session initialized")

                    print("🔍 Listing available tools...")
                    tools_response = await self.session.list_tools()
                    print("✅ Tools listed successfully")
                    return tools_response

                except Exception as e:
                    print(f"❌ Initialization step failed: {e}")
                    raise
            
            # Apply timeout to initialization
            tools_response = await asyncio.wait_for(_initialize_with_timeout(), timeout=timeout)
            
            print("✅ MCP client initialized successfully")
            print(f"✅ Found {len(tools_response.tools)} browser tools:")
            for tool in tools_response.tools:
                print(f"   - {tool.name}: {tool.description}")
            
            return True
            
        except asyncio.TimeoutError:
            print(f"❌ Initialization timeout after {timeout} seconds")
            print("💡 Try checking if xvfb and chrome are properly installed")
            return False
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            print("💡 Check MCP server dependencies and environment")
            return False
    
    async def navigate(self, url: str) -> Dict[str, Any]:
        """Navigate to a URL."""
        if not self.session:
            raise Exception("MCP client not initialized")
            
        print(f"🌐 Navigating to: {url}")
        
        result = await self.session.call_tool(
            "puppeteer_navigate",
            {"url": url}
        )
        
        print("✅ Navigation completed")
        return {"status": "success", "result": result.content[0].text if result.content else ""}
    
    async def screenshot(self, name: str) -> Dict[str, Any]:
        """Take a screenshot."""
        if not self.session:
            raise Exception("MCP client not initialized")
            
        print(f"📸 Taking screenshot: {name}")
        
        result = await self.session.call_tool(
            "puppeteer_screenshot",
            {"name": name}
        )
        
        print("✅ Screenshot captured")
        return {"status": "success", "screenshot": name, "result": result.content[0].text if result.content else ""}
    
    def _parse_mcp_response(self, result) -> Dict[str, Any]:
        """Comprehensive parser for all MCP response formats with enhanced fallback methods."""
        try:
            # Handle None or empty result
            if not result:
                return {"status": "error", "result": "Empty response"}

            # Handle error in response
            if hasattr(result, 'error') or (isinstance(result, dict) and 'error' in result):
                error_msg = result.error if hasattr(result, 'error') else result['error']
                return {"status": "error", "result": f"MCP Error: {error_msg}"}

            # Handle content - multiple format support
            content = None
            if hasattr(result, 'content'):
                content = result.content
            elif isinstance(result, dict) and 'content' in result:
                content = result['content']

            if not content:
                return {"status": "error", "result": "No content in response"}

            # Extract text from content with comprehensive fallback methods
            extracted_texts = []

            if isinstance(content, list):
                for item in content:
                    # Method 1: Direct text attribute
                    if hasattr(item, 'text') and item.text:
                        text = str(item.text)
                        extracted_texts.append(self._clean_extracted_text(text))
                    # Method 2: Dictionary with text key
                    elif isinstance(item, dict) and 'text' in item and item['text']:
                        text = str(item['text'])
                        extracted_texts.append(self._clean_extracted_text(text))
                    # Method 3: Dictionary with data key
                    elif isinstance(item, dict) and 'data' in item and item['data']:
                        text = str(item['data'])
                        extracted_texts.append(self._clean_extracted_text(text))
                    # Method 4: String content directly
                    elif isinstance(item, str) and item:
                        extracted_texts.append(self._clean_extracted_text(item))

            # Method 5: Direct content as string
            elif isinstance(content, str):
                extracted_texts.append(self._clean_extracted_text(content))

            # Return combined results
            if extracted_texts:
                combined_text = "\n".join(extracted_texts).strip()
                if combined_text:
                    return {"status": "success", "result": combined_text}

            return {"status": "error", "result": "Could not extract text from content"}

        except Exception as e:
            return {"status": "error", "result": f"Parse error: {str(e)}"}

    def _clean_extracted_text(self, text: str) -> str:
        """Clean and process extracted text, handling execution wrappers and formatting."""
        if not text:
            return ""

        # Handle Format C: Execution wrapper format
        if "Execution result:" in text:
            lines = text.split('\n')
            content_lines = []

            for line in lines:
                line = line.strip()
                # Skip execution metadata lines
                if (line and
                    not line.startswith('Execution result:') and
                    not line.startswith('Console output:') and
                    not line.startswith('Error:') and
                    not line.startswith('Warning:')):

                    # Remove surrounding quotes if present
                    if line.startswith('"') and line.endswith('"') and len(line) > 2:
                        line = line[1:-1]

                    # Only include substantial content
                    if len(line) > 10:
                        content_lines.append(line)

            if content_lines:
                return '\n'.join(content_lines)

        # Handle direct string results - remove quotes if they wrap the entire content
        if text.startswith('"') and text.endswith('"') and len(text) > 2:
            text = text[1:-1]

        return text.strip()

    async def evaluate(self, script: str, retries: int = 3) -> Dict[str, Any]:
        """Execute JavaScript on the page with robust response parsing and retry logic."""
        if not self.session:
            raise Exception("MCP client not initialized")
            
        print(f"📄 Executing JavaScript...")
        
        for attempt in range(retries):
            try:
                result = await self.session.call_tool(
                    "puppeteer_evaluate",
                    {"script": script}
                )
                
                # Use robust parser
                parsed_result = self._parse_mcp_response(result)
                
                if parsed_result["status"] == "success":
                    print("✅ JavaScript executed successfully")
                    return parsed_result
                elif attempt < retries - 1:
                    print(f"⚠️  Attempt {attempt + 1} failed, retrying...")
                    await asyncio.sleep(1)  # Brief delay before retry
                else:
                    print("❌ JavaScript execution failed after all retries")
                    return parsed_result
                    
            except Exception as e:
                if attempt < retries - 1:
                    print(f"⚠️  Attempt {attempt + 1} error: {e}, retrying...")
                    await asyncio.sleep(1)
                else:
                    print(f"❌ JavaScript execution failed: {e}")
                    return {"status": "error", "result": f"Execution error: {str(e)}"}
        
        return {"status": "error", "result": "All retry attempts failed"}
    
    async def click(self, selector: str) -> Dict[str, Any]:
        """Click an element."""
        if not self.session:
            raise Exception("MCP client not initialized")
            
        print(f"🖱️  Clicking element: {selector}")
        
        result = await self.session.call_tool(
            "puppeteer_click",
            {"selector": selector}
        )
        
        print("✅ Element clicked")
        return {"status": "success", "result": result.content[0].text if result.content else ""}
    
    async def fill(self, selector: str, text: str) -> Dict[str, Any]:
        """Fill an input field."""
        if not self.session:
            raise Exception("MCP client not initialized")
            
        print(f"📝 Filling field {selector} with: {text}")
        
        result = await self.session.call_tool(
            "puppeteer_fill",
            {"selector": selector, "text": text}
        )
        
        print("✅ Field filled")
        return {"status": "success", "result": result.content[0].text if result.content else ""}
    
    async def cleanup(self):
        """Clean up MCP client resources."""
        try:
            if hasattr(self, 'stdio_context') and self.stdio_context:
                await self.stdio_context.__aexit__(None, None, None)
                print("🧹 MCP client cleaned up")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

class WebScraper:
    """Advanced web scraper using pure MCP client."""
    
    def __init__(self):
        self.client = BrowserMCPClient()
    
    async def initialize(self, timeout: int = 60):
        """Initialize the scraper with extended timeout."""
        return await self.client.initialize(timeout=timeout)
    
    async def _wait_for_page_load(self, max_wait_time: int = 15) -> bool:
        """Enhanced page load waiting with multiple verification methods."""
        print("⏳ Waiting for page to fully load with enhanced verification...")

        # Multiple load verification approaches
        verification_methods = [
            {
                "name": "Document ready state check",
                "script": """
                return new Promise((resolve) => {
                    const checkReady = () => {
                        const data = {
                            readyState: document.readyState,
                            bodyExists: !!document.body,
                            contentLength: document.body ? document.body.innerText.length : 0,
                            hasContent: document.body ? document.body.innerText.length > 100 : false,
                            timestamp: Date.now()
                        };
                        resolve(JSON.stringify(data));
                    };

                    if (document.readyState === 'complete') {
                        setTimeout(checkReady, 2000); // Wait for dynamic content
                    } else {
                        document.addEventListener('DOMContentLoaded', () => {
                            setTimeout(checkReady, 2000);
                        });
                    }
                });
                """
            },
            {
                "name": "Content availability check",
                "script": """
                return new Promise((resolve) => {
                    let attempts = 0;
                    const maxAttempts = 10;

                    const checkContent = () => {
                        attempts++;
                        const contentLength = document.body ? document.body.innerText.length : 0;

                        if (contentLength > 100 || attempts >= maxAttempts) {
                            resolve(JSON.stringify({
                                contentLength: contentLength,
                                attempts: attempts,
                                hasSubstantialContent: contentLength > 100,
                                timestamp: Date.now()
                            }));
                        } else {
                            setTimeout(checkContent, 500);
                        }
                    };

                    checkContent();
                });
                """
            }
        ]

        # Try verification methods
        for method in verification_methods:
            print(f"   Verifying with: {method['name']}")

            try:
                load_result = await self.client.evaluate(method['script'], retries=2)

                if load_result['status'] == 'success':
                    try:
                        load_data = json.loads(load_result['result'])

                        if method['name'] == "Document ready state check":
                            print(f"   ✅ Page state: {load_data.get('readyState', 'unknown')}")
                            print(f"   📝 Content length: {load_data.get('contentLength', 0)} chars")

                            if load_data.get('hasContent', False):
                                print("   ✅ Page has substantial content")
                                return True

                        elif method['name'] == "Content availability check":
                            content_length = load_data.get('contentLength', 0)
                            print(f"   📝 Final content check: {content_length} chars after {load_data.get('attempts', 0)} attempts")

                            if load_data.get('hasSubstantialContent', False):
                                print("   ✅ Substantial content detected")
                                return True

                    except json.JSONDecodeError:
                        print(f"   ⚠️  Could not parse {method['name']} result")
                        continue
                else:
                    print(f"   ❌ {method['name']} failed: {load_result.get('result', 'Unknown error')}")

            except Exception as e:
                print(f"   ❌ {method['name']} exception: {e}")
                continue

        # Fallback: simple wait
        print("   ⏳ Fallback: Simple wait for page stabilization...")
        await asyncio.sleep(3)

        print("⚠️  Enhanced page load verification completed with fallback")
        return False

    async def _extract_content_with_fallbacks(self) -> Dict[str, Any]:
        """Extract content using multiple fallback methods for maximum reliability."""
        print("📝 Extracting content with comprehensive fallback methods...")

        # Define multiple extraction methods in order of preference
        extraction_methods = [
            {
                "name": "Enhanced innerText",
                "script": """
                try {
                    // Method 1: Enhanced innerText with cleanup
                    let content = document.body?.innerText || '';

                    // Clean up excessive whitespace
                    content = content.replace(/\\s+/g, ' ').trim();

                    return content || 'No content found';
                } catch (e) {
                    return 'Error: ' + e.message;
                }
                """
            },
            {
                "name": "Enhanced textContent",
                "script": """
                try {
                    // Method 2: Enhanced textContent with filtering
                    let content = document.body?.textContent || '';

                    // Clean up and filter
                    content = content.replace(/\\s+/g, ' ').trim();

                    return content || 'No content found';
                } catch (e) {
                    return 'Error: ' + e.message;
                }
                """
            },
            {
                "name": "Selective element extraction",
                "script": """
                try {
                    // Method 3: Extract from specific content elements
                    const selectors = [
                        'main', 'article', '.content', '#content', '.main-content',
                        'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                        'li', 'td', 'th', 'section', 'aside'
                    ];

                    let allText = [];

                    for (const selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim();
                            if (text && text.length > 10 && !allText.includes(text)) {
                                allText.push(text);
                            }
                        }
                    }

                    const content = allText.join(' ').replace(/\\s+/g, ' ').trim();
                    return content || 'No content found';
                } catch (e) {
                    return 'Error: ' + e.message;
                }
                """
            },
            {
                "name": "Tree walker method",
                "script": """
                try {
                    // Method 4: Tree walker for all text nodes
                    if (!document.body) return 'No body element';

                    const walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        {
                            acceptNode: function(node) {
                                // Filter out script and style content
                                const parent = node.parentElement;
                                if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
                                    return NodeFilter.FILTER_REJECT;
                                }
                                return NodeFilter.FILTER_ACCEPT;
                            }
                        },
                        false
                    );

                    const textNodes = [];
                    let node;
                    while (node = walker.nextNode()) {
                        const text = node.textContent.trim();
                        if (text.length > 5) {
                            textNodes.push(text);
                        }
                    }

                    const content = textNodes.join(' ').replace(/\\s+/g, ' ').trim();
                    return content || 'No content found';
                } catch (e) {
                    return 'Error: ' + e.message;
                }
                """
            },
            {
                "name": "Comprehensive extraction",
                "script": """
                try {
                    // Method 5: Comprehensive approach combining multiple techniques
                    let content = '';

                    // Try different approaches
                    const approaches = [
                        () => document.body?.innerText,
                        () => document.body?.textContent,
                        () => document.documentElement?.innerText,
                        () => document.documentElement?.textContent,
                        () => {
                            const elements = document.querySelectorAll('*');
                            return Array.from(elements)
                                .map(el => el.textContent?.trim())
                                .filter(text => text && text.length > 10)
                                .join(' ');
                        }
                    ];

                    for (const approach of approaches) {
                        try {
                            const result = approach();
                            if (result && result.length > 100) {
                                content = result;
                                break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }

                    // Clean up the content
                    content = content.replace(/\\s+/g, ' ').trim();

                    return content || 'No substantial content found';
                } catch (e) {
                    return 'Error: ' + e.message;
                }
                """
            }
        ]

        # Try each method until we get substantial content
        for method in extraction_methods:
            print(f"   Trying: {method['name']}")

            try:
                result = await self.client.evaluate(method['script'], retries=2)

                if result['status'] == 'success':
                    content = result['result']

                    # Check if we got substantial content (more than 100 characters)
                    if content and len(content) > 100 and not content.startswith('Error:'):
                        print(f"   ✅ Success with {method['name']}: {len(content)} characters")
                        return {"status": "success", "result": content}
                    else:
                        print(f"   ⚠️  {method['name']} returned insufficient content: {len(content) if content else 0} chars")
                else:
                    print(f"   ❌ {method['name']} failed: {result.get('result', 'Unknown error')}")

            except Exception as e:
                print(f"   ❌ {method['name']} exception: {e}")
                continue

        # If all methods failed, return error
        print("❌ All content extraction methods failed")
        return {"status": "error", "result": "All content extraction methods failed"}

    async def scrape_website(self, url: str, max_retries: int = 3) -> Dict[str, Any]:
        """Scrape a complete website with enhanced content extraction and retry logic."""
        print(f"🕷️  SCRAPING WEBSITE: {url}")
        print("=" * 60)

        # Retry logic for the entire scraping process
        for attempt in range(max_retries):
            if attempt > 0:
                print(f"\n🔄 RETRY ATTEMPT {attempt + 1}/{max_retries}")
                print("=" * 40)
                await asyncio.sleep(2)  # Brief delay between retries

            try:
                result = await self._scrape_website_single_attempt(url)

                # Check if we got substantial content
                if (result.get('status') == 'success' and
                    result.get('content_length', 0) > 100):
                    print(f"✅ Scraping successful on attempt {attempt + 1}")
                    return result
                elif attempt < max_retries - 1:
                    print(f"⚠️  Attempt {attempt + 1} yielded insufficient content, retrying...")
                    continue
                else:
                    print(f"❌ All {max_retries} attempts failed to extract substantial content")
                    return result

            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"⚠️  Attempt {attempt + 1} failed with error: {e}, retrying...")
                    continue
                else:
                    print(f"❌ All {max_retries} attempts failed")
                    return {
                        "url": url,
                        "status": "error",
                        "error": f"All retry attempts failed. Last error: {str(e)}",
                        "scraped_at": datetime.now().isoformat()
                    }

        # This should not be reached, but just in case
        return {
            "url": url,
            "status": "error",
            "error": "Unexpected end of retry loop",
            "scraped_at": datetime.now().isoformat()
        }

    async def _scrape_website_single_attempt(self, url: str) -> Dict[str, Any]:
        """Single attempt at scraping a website."""
        try:
            # Navigate to website
            print(f"🌐 Navigating to: {url}")
            nav_result = await self.client.navigate(url)

            # Enhanced page load waiting
            page_loaded = await self._wait_for_page_load()

            # Take screenshot
            timestamp = datetime.now().strftime('%H%M%S')
            screenshot_name = f"scrape_{timestamp}"
            print(f"📸 Taking screenshot: {screenshot_name}")
            screenshot_result = await self.client.screenshot(screenshot_name)

            # Extract page title with enhanced fallbacks
            print("📋 Extracting page title...")
            title_script = """
            try {
                return document.title ||
                       document.querySelector('title')?.textContent ||
                       document.querySelector('h1')?.textContent ||
                       document.querySelector('meta[property="og:title"]')?.content ||
                       'No title found';
            } catch (e) {
                return 'Title extraction error: ' + e.message;
            }
            """
            title_result = await self.client.evaluate(title_script, retries=2)

            # Extract complete page content with enhanced multiple methods and retry logic
            print("📄 Starting comprehensive content extraction...")
            content_result = await self._extract_content_with_fallbacks()
            
            # Extract page structure with enhanced reliability
            structure_script = """
            try {
                const result = {
                    links: document.querySelectorAll('a').length,
                    images: document.querySelectorAll('img').length,
                    forms: document.querySelectorAll('form').length,
                    headings: {
                        h1: Array.from(document.querySelectorAll('h1')).map(h => h.textContent?.trim() || '').filter(t => t),
                        h2: Array.from(document.querySelectorAll('h2')).map(h => h.textContent?.trim() || '').filter(t => t),
                        h3: Array.from(document.querySelectorAll('h3')).map(h => h.textContent?.trim() || '').filter(t => t)
                    },
                    paragraphs: document.querySelectorAll('p').length,
                    totalElements: document.querySelectorAll('*').length,
                    hasNavigation: !!document.querySelector('nav'),
                    hasFooter: !!document.querySelector('footer'),
                    hasHeader: !!document.querySelector('header')
                };
                return JSON.stringify(result);
            } catch (e) {
                return JSON.stringify({error: e.message, fallback: true});
            }
            """
            structure_result = await self.client.evaluate(structure_script)
            
            # Extract links with enhanced error handling
            links_script = """
            try {
                const links = Array.from(document.querySelectorAll('a[href]'))
                    .slice(0, 50) // Limit to first 50 links to avoid huge responses
                    .map(a => {
                        try {
                            return {
                                text: (a.textContent || '').trim().substring(0, 100),
                                href: a.href || '',
                                title: a.title || '',
                                isExternal: a.href && !a.href.includes(window.location.hostname)
                            };
                        } catch (e) {
                            return {text: 'Error extracting link', href: '', title: '', isExternal: false};
                        }
                    })
                    .filter(link => link.text || link.href);
                return JSON.stringify(links);
            } catch (e) {
                return JSON.stringify([{error: e.message}]);
            }
            """
            links_result = await self.client.evaluate(links_script)
            
            # Extract contact information with comprehensive patterns
            contact_script = """
            try {
                const text = document.body?.innerText || document.body?.textContent || '';
                const result = {
                    emails: [],
                    phones: [],
                    emailLinks: [],
                    phoneLinks: [],
                    socialLinks: []
                };
                
                // Enhanced email patterns
                const emailPatterns = [
                    /[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}/g,
                    /[\\w\\.-]+\\s*@\\s*[\\w\\.-]+\\s*\\.\\s*[a-zA-Z]{2,}/g
                ];
                
                emailPatterns.forEach(pattern => {
                    const matches = text.match(pattern) || [];
                    result.emails.push(...matches);
                });
                
                // Enhanced phone patterns
                const phonePatterns = [
                    /[\\+]?[\\d\\s\\-\\(\\)]{10,}/g,
                    /\\(?\\d{3}\\)?[\\s\\-]?\\d{3}[\\s\\-]?\\d{4}/g,
                    /\\+\\d{1,3}[\\s\\-]?\\(?\\d{1,4}\\)?[\\s\\-]?\\d{1,4}[\\s\\-]?\\d{1,9}/g
                ];
                
                phonePatterns.forEach(pattern => {
                    const matches = text.match(pattern) || [];
                    result.phones.push(...matches.filter(p => p.replace(/\\D/g, '').length >= 10));
                });
                
                // Extract email and phone links
                result.emailLinks = Array.from(document.querySelectorAll('a[href^="mailto:"]'))
                    .map(a => a.href.replace('mailto:', ''));
                result.phoneLinks = Array.from(document.querySelectorAll('a[href^="tel:"]'))
                    .map(a => a.href.replace('tel:', ''));
                
                // Extract social media links
                const socialDomains = ['facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 'youtube.com'];
                result.socialLinks = Array.from(document.querySelectorAll('a[href]'))
                    .filter(a => socialDomains.some(domain => a.href.includes(domain)))
                    .map(a => ({platform: socialDomains.find(d => a.href.includes(d)), url: a.href}))
                    .slice(0, 10);
                
                // Remove duplicates
                result.emails = [...new Set(result.emails)];
                result.phones = [...new Set(result.phones)];
                result.emailLinks = [...new Set(result.emailLinks)];
                result.phoneLinks = [...new Set(result.phoneLinks)];
                
                return JSON.stringify(result);
            } catch (e) {
                return JSON.stringify({error: e.message, emails: [], phones: [], emailLinks: [], phoneLinks: []});
            }
            """
            contact_result = await self.client.evaluate(contact_script)
            
            # Parse results with robust error handling
            def safe_json_parse(result, default_value):
                """Safely parse JSON results with fallback."""
                if result['status'] != 'success':
                    print(f"⚠️  Result parsing failed: {result.get('result', 'Unknown error')}")
                    return default_value
                
                try:
                    return json.loads(result['result'])
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON decode error: {e}")
                    return default_value
                except Exception as e:
                    print(f"⚠️  Parse error: {e}")
                    return default_value
            
            structure_data = safe_json_parse(structure_result, {})
            links_data = safe_json_parse(links_result, [])
            contact_data = safe_json_parse(contact_result, {})
            
            # Compile results with enhanced error handling
            title_text = title_result['result'] if title_result['status'] == 'success' else "No title"
            content_text = content_result['result'] if content_result['status'] == 'success' else "No content"
            content_length = len(content_text) if content_text != "No content" else 0
            word_count = len(content_text.split()) if content_text != "No content" else 0

            # Determine overall success based on content extraction
            overall_status = "success" if content_length > 100 else "partial_success" if content_length > 0 else "failed"

            scraped_data = {
                "url": url,
                "scraped_at": datetime.now().isoformat(),
                "screenshot": screenshot_name,
                "status": overall_status,
                "title": title_text,
                "content": content_text,
                "content_length": content_length,
                "word_count": word_count,
                "page_loaded_successfully": page_loaded,
                "extraction_method": "enhanced_fallback_methods",
                "structure": structure_data,
                "links": links_data,
                "contact_info": contact_data
            }
            
            # Enhanced results display
            print("\n📊 ENHANCED SCRAPING RESULTS:")
            print("=" * 50)

            # Status with color coding
            status_emoji = {
                "success": "✅",
                "partial_success": "⚠️ ",
                "failed": "❌"
            }
            print(f"{status_emoji.get(scraped_data['status'], '❓')} Status: {scraped_data['status'].upper()}")
            print(f"📋 Title: {scraped_data['title']}")
            print(f"📝 Content: {scraped_data['content_length']} chars, {scraped_data['word_count']} words")
            print(f"📸 Screenshot: {scraped_data['screenshot']}")
            print(f"🔧 Method: {scraped_data['extraction_method']}")
            print(f"⏳ Page Load: {'✅ Success' if scraped_data['page_loaded_successfully'] else '⚠️  Fallback'}")

            if structure_data:
                print(f"🔗 Links: {structure_data.get('links', 0)}")
                print(f"🖼️  Images: {structure_data.get('images', 0)}")
                print(f"📝 Forms: {structure_data.get('forms', 0)}")
                print(f"📊 Total Elements: {structure_data.get('totalElements', 0)}")

                headings = structure_data.get('headings', {})
                if headings.get('h1'):
                    print(f"📋 H1 Headings: {headings['h1']}")

            if contact_data and not contact_data.get('error'):
                emails = list(set(contact_data.get('emails', []) + contact_data.get('emailLinks', [])))
                phones = list(set(contact_data.get('phones', []) + contact_data.get('phoneLinks', [])))
                social_links = contact_data.get('socialLinks', [])

                if emails:
                    print(f"📧 Emails: {emails[:5]}{'...' if len(emails) > 5 else ''}")
                if phones:
                    print(f"📞 Phones: {phones[:3]}{'...' if len(phones) > 3 else ''}")
                if social_links:
                    print(f"🔗 Social: {[s.get('platform') for s in social_links[:3]]}")
            elif contact_data.get('error'):
                print(f"⚠️  Contact extraction error: {contact_data['error']}")

            # Enhanced content preview
            if scraped_data['content'] and scraped_data['content'] != "No content":
                print(f"\n📄 CONTENT PREVIEW (First 500 chars):")
                print("-" * 50)
                preview = scraped_data['content'][:500]
                print(f"{preview}...")
                print("-" * 50)
            else:
                print(f"\n❌ NO CONTENT EXTRACTED")

            return scraped_data
            
        except Exception as e:
            print(f"❌ Single attempt scraping failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                "url": url,
                "status": "error",
                "error": str(e),
                "scraped_at": datetime.now().isoformat(),
                "content_length": 0,
                "word_count": 0,
                "extraction_method": "failed"
            }

async def main():
    """Test the pure browser MCP scraper."""
    print("🕷️  PURE BROWSER MCP SCRAPER")
    print("=" * 50)
    
    # Test URL
    test_url = "https://itpyx.pk"
    
    scraper = None
    try:
        # Initialize scraper
        scraper = WebScraper()
        init_success = await scraper.initialize()
        
        if not init_success:
            print("❌ Failed to initialize scraper")
            return
        
        # Scrape website
        result = await scraper.scrape_website(test_url)
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"pure_mcp_scraping_results_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Results saved to: {output_file}")

        # Enhanced success reporting
        status = result.get('status', 'unknown')
        content_length = result.get('content_length', 0)

        if status == 'success':
            print("\n🎉 ENHANCED MCP SCRAPING SUCCESSFUL!")
            print(f"📊 Content extracted: {content_length} characters")
            print(f"📝 Word count: {result.get('word_count', 0)} words")
            print(f"🔧 Method: {result.get('extraction_method', 'unknown')}")
        elif status == 'partial_success':
            print("\n⚠️  PARTIAL SUCCESS - Some content extracted")
            print(f"📊 Content extracted: {content_length} characters")
            print("💡 Consider checking the website manually for dynamic content")
        else:
            print("\n❌ Scraping failed")
            print(f"🔍 Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up resources
        if scraper and scraper.client:
            await scraper.client.cleanup()

if __name__ == "__main__":
    print("🚀 Pure Browser MCP Scraper")
    print("Direct MCP client without lead generation components")
    print()
    
    asyncio.run(main())