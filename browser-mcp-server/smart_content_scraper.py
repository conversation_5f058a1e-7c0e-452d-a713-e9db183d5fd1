#!/usr/bin/env python3
"""
🧠 SMART CONTENT SCRAPER - Intelligent Content Filtering
Extracts only the main, relevant content from websites while filtering out:
- Navigation menus
- Footer links  
- JavaScript data
- Repeated content
- Metadata
"""

import asyncio
import json
import re
from datetime import datetime
from enhanced_direct_scraper import EnhancedDirectScraper

class SmartContentScraper:
    """Intelligent scraper that filters out irrelevant content."""
    
    def __init__(self):
        self.scraper = EnhancedDirectScraper()
        
    def get_user_input(self):
        """Get website URL from user with validation."""
        print("🧠 SMART CONTENT SCRAPER")
        print("=" * 50)
        print("This tool extracts ONLY the main, relevant content from websites.")
        print("It automatically filters out navigation, footers, and irrelevant data.")
        print()
        
        while True:
            url = input("📝 Enter the website URL to scrape: ").strip()
            
            if not url:
                print("❌ Please enter a valid URL!")
                continue
                
            # Add https:// if no protocol specified
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                print(f"📋 Using URL: {url}")
            
            return url

    def clean_and_filter_content(self, raw_content: str) -> dict:
        """Apply intelligent filtering to extract only relevant content."""
        print("🧠 Applying smart content filtering...")
        
        if not raw_content or len(raw_content) < 50:
            return {
                "filtered_content": "",
                "original_length": 0,
                "filtered_length": 0,
                "reduction_percentage": 0,
                "filters_applied": ["content_too_short"]
            }
        
        original_length = len(raw_content)
        content = raw_content
        filters_applied = []
        
        # Filter 1: Remove JavaScript data blocks
        js_patterns = [
            r'window\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*\{.*?\};',  # window.varName = {...};
            r'\{[^{}]*"[a-zA-Z_$][a-zA-Z0-9_$]*":\s*\{.*?\}[^{}]*\}',  # Large JSON objects
            r'"[a-zA-Z0-9_-]{20,}":\s*"[^"]{100,}"',  # Long key-value pairs
            r'https?://[^\s"\']{50,}',  # Very long URLs
        ]
        
        for pattern in js_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("javascript_data_removed")
        
        # Filter 2: Remove repeated navigation patterns
        nav_patterns = [
            r'(Shop\s+Mac|Shop\s+iPhone|Shop\s+iPad|Explore\s+\w+){3,}',  # Repeated shop/explore
            r'(Support|Help|Contact|About|Privacy|Terms){3,}',  # Repeated footer links
            r'(\w+\s+\w+\s+){10,}',  # Very long sequences of repeated words
        ]
        
        for pattern in nav_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("navigation_patterns_removed")
        
        # Filter 3: Remove excessive whitespace and clean up
        content = re.sub(r'\s+', ' ', content)  # Multiple spaces to single
        content = re.sub(r'\n\s*\n', '\n', content)  # Multiple newlines
        content = content.strip()
        filters_applied.append("whitespace_normalized")
        
        # Filter 4: Extract main content sections
        main_content_indicators = [
            r'<main[^>]*>(.*?)</main>',
            r'<article[^>]*>(.*?)</article>',
            r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
            r'<section[^>]*class="[^"]*main[^"]*"[^>]*>(.*?)</section>',
        ]
        
        # Try to extract from main content areas first
        main_content_found = False
        for pattern in main_content_indicators:
            matches = re.findall(pattern, raw_content, re.DOTALL | re.IGNORECASE)
            if matches:
                # Use the longest match as main content
                longest_match = max(matches, key=len)
                if len(longest_match) > 200:  # Substantial content
                    content = longest_match
                    content = re.sub(r'<[^>]+>', '', content)  # Remove HTML tags
                    content = re.sub(r'\s+', ' ', content).strip()
                    main_content_found = True
                    filters_applied.append("main_content_extracted")
                    break
        
        # Filter 5: Remove common noise patterns
        noise_patterns = [
            r'Cookie\s+Policy|Privacy\s+Policy|Terms\s+of\s+Service',
            r'Subscribe\s+to\s+newsletter|Sign\s+up\s+for\s+updates',
            r'Follow\s+us\s+on|Social\s+media|Share\s+this',
            r'Copyright\s+©|All\s+rights\s+reserved',
            r'Loading\.\.\.|Please\s+wait|Error\s+\d+',
        ]
        
        for pattern in noise_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("noise_patterns_removed")
        
        # Filter 6: Extract meaningful sentences
        sentences = re.split(r'[.!?]+', content)
        meaningful_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            # Keep sentences that are substantial and meaningful
            if (len(sentence) > 20 and 
                len(sentence.split()) > 3 and
                not re.match(r'^[A-Z\s]+$', sentence) and  # Not all caps
                not sentence.lower().startswith(('click', 'learn more', 'shop now', 'buy now'))):
                meaningful_sentences.append(sentence)
        
        if meaningful_sentences:
            content = '. '.join(meaningful_sentences) + '.'
            filters_applied.append("meaningful_sentences_extracted")
        
        # Final cleanup
        content = re.sub(r'\s+', ' ', content).strip()
        
        filtered_length = len(content)
        reduction_percentage = ((original_length - filtered_length) / original_length * 100) if original_length > 0 else 0
        
        return {
            "filtered_content": content,
            "original_length": original_length,
            "filtered_length": filtered_length,
            "reduction_percentage": round(reduction_percentage, 1),
            "filters_applied": list(set(filters_applied))
        }

    async def scrape_smart_content(self, url: str):
        """Scrape website with intelligent content filtering."""
        print(f"🧠 SMART CONTENT SCRAPING: {url}")
        print("=" * 60)
        
        try:
            # Start the scraper
            if await self.scraper.start_mcp_server():
                print("✅ Browser started successfully!")
                
                # Get raw content using enhanced scraper
                print("📄 Extracting raw content...")
                result = await self.scraper.scrape_website_enhanced(url)
                
                if result and result.get('status') == 'success':
                    raw_content = result.get('content', '')
                    
                    # Apply smart filtering
                    filtered_result = self.clean_and_filter_content(raw_content)
                    
                    # Create enhanced result
                    smart_result = {
                        "url": url,
                        "scraped_at": datetime.now().isoformat(),
                        "status": "success",
                        "extraction_method": "smart_content_filtering",
                        "screenshot": result.get('screenshot'),
                        
                        # Smart content
                        "smart_content": filtered_result["filtered_content"],
                        "smart_content_length": filtered_result["filtered_length"],
                        "smart_word_count": len(filtered_result["filtered_content"].split()),
                        
                        # Original content (for comparison)
                        "raw_content": raw_content,
                        "raw_content_length": filtered_result["original_length"],
                        "raw_word_count": len(raw_content.split()),
                        
                        # Filtering stats
                        "content_reduction": f"{filtered_result['reduction_percentage']}%",
                        "filters_applied": filtered_result["filters_applied"]
                    }
                    
                    # Display results
                    await self._display_smart_results(smart_result)
                    
                    # Save results
                    filename = await self._save_smart_results(smart_result)
                    
                    return smart_result, filename
                else:
                    print("❌ Failed to extract raw content")
                    return None, None
            else:
                print("❌ Failed to start browser")
                return None, None
                
        except Exception as e:
            print(f"❌ Error occurred: {e}")
            return None, None
        finally:
            await self.scraper.cleanup()

    async def _display_smart_results(self, result):
        """Display smart filtering results."""
        print("\n" + "=" * 60)
        print("🧠 SMART CONTENT FILTERING RESULTS")
        print("=" * 60)
        
        print("✅ SUCCESS! Smart content extracted successfully!")
        print()
        
        # Content statistics
        print(f"📊 CONTENT STATISTICS:")
        print(f"   • Original: {result['raw_content_length']:,} characters ({result['raw_word_count']:,} words)")
        print(f"   • Filtered: {result['smart_content_length']:,} characters ({result['smart_word_count']:,} words)")
        print(f"   • Reduction: {result['content_reduction']} smaller")
        print()
        
        # Filters applied
        print(f"🔧 FILTERS APPLIED:")
        for filter_name in result['filters_applied']:
            print(f"   ✅ {filter_name.replace('_', ' ').title()}")
        print()
        
        # Smart content preview
        smart_content = result['smart_content']
        if smart_content:
            print("📄 SMART CONTENT PREVIEW (First 500 characters):")
            print("-" * 50)
            preview = smart_content[:500]
            print(f'"{preview}..."')
            print("-" * 50)
        else:
            print("⚠️  No smart content extracted")

    async def _save_smart_results(self, result):
        """Save smart results to JSON file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"smart_content_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n💾 Smart results saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"⚠️  Could not save results: {e}")
            return None

async def main():
    """Main function for smart content scraping."""
    print("🎉 Welcome to Smart Content Scraper!")
    print("This tool extracts only the main, relevant content from websites.")
    print("It filters out navigation, footers, and irrelevant data automatically.")
    print()
    
    scraper = SmartContentScraper()
    
    try:
        # Get URL from user
        url = scraper.get_user_input()
        
        print(f"\n🚀 Starting Smart Content Scraping")
        print(f"🎯 Target: {url}")
        print("⏳ This may take 30-60 seconds...")
        print()
        
        # Scrape with smart filtering
        result, filename = await scraper.scrape_smart_content(url)
        
        print("\n" + "=" * 60)
        
        if result and result.get('status') == 'success':
            print("🎉 SMART SCRAPING COMPLETED SUCCESSFULLY!")
            print()
            print("📁 What you got:")
            print(f"   • Clean, relevant content: {result['smart_content_length']:,} characters")
            print(f"   • Content reduction: {result['content_reduction']}")
            print(f"   • Word count: {result['smart_word_count']:,} words")
            print(f"   • Screenshot: {result.get('screenshot', 'N/A')}")
            print(f"   • Results file: {filename}")
            print()
            print("💡 Benefits:")
            print("   • No navigation menus or footer links")
            print("   • No JavaScript data or metadata")
            print("   • Only main, meaningful content")
            print("   • Much cleaner and more relevant")
            
        else:
            print("❌ SMART SCRAPING FAILED")
            print("🔧 Please try again or check the website URL")
        
        print("\n🙏 Thank you for using Smart Content Scraper!")
        
    except KeyboardInterrupt:
        print("\n\n👋 Scraping cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
