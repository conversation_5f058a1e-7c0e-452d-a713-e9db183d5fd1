#!/bin/bash
# Browser MCP Server Installation Script

echo "🚀 Installing Browser MCP Server..."
echo "=================================="

# Check Python version
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
    echo "❌ Error: Python 3.8+ required. Current version: $python_version"
    exit 1
fi
echo "✅ Python version check passed: $python_version"

# Check Node.js version
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is required but not installed."
    echo "   Please install Node.js from https://nodejs.org/"
    exit 1
fi
node_version=$(node --version | grep -oP '\d+')
if [[ $node_version -lt 16 ]]; then
    echo "❌ Error: Node.js 16+ required. Current version: $(node --version)"
    exit 1
fi
echo "✅ Node.js version check passed: $(node --version)"

# Create virtual environment
echo "📦 Creating Python virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs screenshots config

# Copy configuration template if secrets file doesn't exist
if [ ! -f "config/mcp_agent.secrets.yaml" ]; then
    echo "📋 Creating configuration template..."
    cp config/mcp_agent.secrets.yaml.example config/mcp_agent.secrets.yaml
    echo "⚠️  Please edit config/mcp_agent.secrets.yaml with your API keys"
fi

# Test installation
echo "🧪 Testing installation..."
python3 -c "
try:
    from src.agents.mcp_browser_agent import MCPLeadGenerationAgent
    print('✅ Browser MCP Agent import successful')
except ImportError as e:
    print(f'❌ Import error: {e}')
    exit(1)
"

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit config/mcp_agent.secrets.yaml with your API keys"
echo "2. Run: source venv/bin/activate"
echo "3. Test: python examples/test_basic_scraping.py"
echo ""
echo "For more information, see README.md"