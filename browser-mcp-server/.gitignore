# Browser MCP Server - Git Ignore

# Environment and API Keys
.env
*.env
.env.local
.env.production
config/mcp_agent.secrets.yaml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Screenshots and logs
screenshots/*.png
screenshots/*.jpg
screenshots/*.jpeg
logs/*.log
logs/*.jsonl

# Keep directory structure
!screenshots/.gitkeep
!logs/.gitkeep

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# System files
*.log
*.tmp
*.temp
.cache/
.pytest_cache/

# Browser automation
.puppeteer_cache/
chromium/

# Test files
test_*.py
debug_*.py
temp_*.py

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover