#!/usr/bin/env python3
"""
🔍 URL DISCOVERY ENGINE - Phase 1
Discovers all URLs from a website using multiple methods:
1. Sitemap.xml parsing
2. Robots.txt analysis
3. Internal link extraction
4. Recursive crawling
"""

import asyncio
import requests
import xml.etree.ElementTree as ET
import re
import json
from urllib.parse import urljoin, urlparse, urlunparse
from urllib.robotparser import RobotFileParser
from datetime import datetime
from typing import Set, List, Dict, Optional
# Standalone URL discovery - no scraper dependency

class URLDiscoveryEngine:
    """Comprehensive URL discovery for entire websites."""
    
    def __init__(self, domain: str):
        self.domain = self.normalize_domain(domain)
        self.base_url = f"https://{self.domain}"
        self.discovered_urls = set()
# No scraper dependency - using simple HTTP requests
        
        # URL filtering patterns
        self.exclude_patterns = [
            r'/admin/', r'/login/', r'/api/', r'/wp-admin/',
            r'\.pdf$', r'\.jpg$', r'\.jpeg$', r'\.png$', r'\.gif$',
            r'\.css$', r'\.js$', r'\.xml$', r'\.txt$',
            r'/search\?', r'/tag/', r'/category/', r'/author/',
            r'#', r'\?utm_', r'/feed/', r'/rss/', r'/atom/',
            r'/wp-content/', r'/wp-includes/', r'/wp-json/',
            r'\.zip$', r'\.rar$', r'\.exe$', r'\.dmg$'
        ]
        
        self.priority_patterns = [
            (r'^/$', 10),  # Homepage - highest priority
            (r'/about', 9), (r'/services', 9), (r'/products', 9),
            (r'/contact', 8), (r'/blog/', 7), (r'/news/', 7),
            (r'/pricing', 6), (r'/features', 6), (r'/solutions', 6)
        ]
    
    def normalize_domain(self, domain: str) -> str:
        """Normalize domain to consistent format."""
        domain = domain.strip().lower()
        # Remove protocol if present
        domain = re.sub(r'^https?://', '', domain)
        # Remove www if present
        domain = re.sub(r'^www\.', '', domain)
        # Remove trailing slash
        domain = domain.rstrip('/')
        return domain
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and should be included."""
        if not url or not isinstance(url, str):
            return False
        
        # Parse URL
        try:
            parsed = urlparse(url)
        except:
            return False
        
        # Must be HTTP/HTTPS
        if parsed.scheme not in ['http', 'https']:
            return False
        
        # Must be same domain
        url_domain = parsed.netloc.lower()
        url_domain = re.sub(r'^www\.', '', url_domain)
        if url_domain != self.domain:
            return False
        
        # Check exclude patterns
        for pattern in self.exclude_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        return True
    
    def calculate_url_priority(self, url: str) -> int:
        """Calculate priority score for URL."""
        for pattern, priority in self.priority_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return priority
        return 5  # Default priority
    
    def parse_sitemap(self, sitemap_url: str) -> List[Dict]:
        """Parse XML sitemap and extract URLs with metadata."""
        print(f"📄 Parsing sitemap: {sitemap_url}")
        
        try:
            response = requests.get(sitemap_url, timeout=10)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            urls = []
            
            # Handle sitemap index (contains links to other sitemaps)
            if 'sitemapindex' in root.tag:
                print("   📋 Found sitemap index")
                for sitemap in root.findall('.//{*}sitemap'):
                    loc_elem = sitemap.find('{*}loc')
                    if loc_elem is not None:
                        nested_urls = self.parse_sitemap(loc_elem.text)
                        urls.extend(nested_urls)
            
            # Handle URL set (contains actual URLs)
            else:
                print("   🔗 Found URL set")
                for url_elem in root.findall('.//{*}url'):
                    loc_elem = url_elem.find('{*}loc')
                    if loc_elem is not None:
                        url = loc_elem.text
                        
                        if self.is_valid_url(url):
                            # Extract metadata
                            lastmod_elem = url_elem.find('{*}lastmod')
                            priority_elem = url_elem.find('{*}priority')
                            changefreq_elem = url_elem.find('{*}changefreq')
                            
                            url_data = {
                                'url': url,
                                'source': 'sitemap',
                                'lastmod': lastmod_elem.text if lastmod_elem is not None else None,
                                'priority': float(priority_elem.text) if priority_elem is not None else 0.5,
                                'changefreq': changefreq_elem.text if changefreq_elem is not None else None,
                                'calculated_priority': self.calculate_url_priority(url)
                            }
                            urls.append(url_data)
            
            print(f"   ✅ Found {len(urls)} URLs in sitemap")
            return urls
            
        except Exception as e:
            print(f"   ❌ Sitemap parsing failed: {e}")
            return []
    
    def parse_robots_txt(self) -> List[str]:
        """Parse robots.txt to find additional sitemaps."""
        robots_url = f"{self.base_url}/robots.txt"
        print(f"🤖 Parsing robots.txt: {robots_url}")
        
        sitemaps = []
        try:
            response = requests.get(robots_url, timeout=10)
            response.raise_for_status()
            
            for line in response.text.split('\n'):
                line = line.strip()
                if line.lower().startswith('sitemap:'):
                    sitemap_url = line.split(':', 1)[1].strip()
                    sitemaps.append(sitemap_url)
            
            print(f"   ✅ Found {len(sitemaps)} sitemaps in robots.txt")
            return sitemaps
            
        except Exception as e:
            print(f"   ❌ Robots.txt parsing failed: {e}")
            return []
    
    def extract_links_from_html(self, url: str, html_content: str) -> List[str]:
        """Extract all internal links from HTML content using simple parsing."""
        print(f"🔗 Extracting links from HTML: {url}")

        try:
            import re
            from urllib.parse import urljoin

            # Find all href attributes
            href_pattern = r'href=["\']([^"\']+)["\']'
            matches = re.findall(href_pattern, html_content, re.IGNORECASE)

            internal_links = []
            for href in matches:
                # Convert relative URLs to absolute
                try:
                    absolute_url = urljoin(url, href)
                    if self.is_valid_url(absolute_url):
                        internal_links.append(absolute_url)
                except:
                    continue

            # Remove duplicates
            internal_links = list(set(internal_links))

            print(f"   ✅ Found {len(internal_links)} internal links")
            return internal_links

        except Exception as e:
            print(f"   ❌ Link extraction failed: {e}")
            return []

    def fetch_page_content(self, url: str) -> Optional[str]:
        """Fetch page content using simple HTTP request."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"   ❌ Failed to fetch {url}: {e}")
            return None
    
    def discover_urls_recursive(self, start_url: str, max_depth: int = 2, visited: Optional[Set] = None) -> List[Dict]:
        """Recursively discover URLs by following internal links."""
        if visited is None:
            visited = set()

        if start_url in visited or max_depth <= 0:
            return []

        visited.add(start_url)
        discovered = []

        print(f"🕷️  Crawling: {start_url} (depth: {max_depth})")

        try:
            # Fetch page content
            html_content = self.fetch_page_content(start_url)
            if not html_content:
                return discovered

            # Extract links from HTML
            links = self.extract_links_from_html(start_url, html_content)

            for link in links:
                if link not in visited and self.is_valid_url(link):
                    url_data = {
                        'url': link,
                        'source': 'crawling',
                        'parent_url': start_url,
                        'depth': 3 - max_depth,  # Calculate actual depth
                        'calculated_priority': self.calculate_url_priority(link)
                    }
                    discovered.append(url_data)

                    # Recursive crawl with reduced depth (limit to avoid infinite loops)
                    if max_depth > 1 and len(discovered) < 50:  # Limit total discovered URLs per recursion
                        nested_urls = self.discover_urls_recursive(link, max_depth - 1, visited)
                        discovered.extend(nested_urls)

        except Exception as e:
            print(f"   ❌ Crawling failed for {start_url}: {e}")

        return discovered
    
    def discover_all_urls(self, max_crawl_depth: int = 2) -> Dict:
        """Main method to discover all URLs using multiple methods."""
        print(f"🚀 STARTING URL DISCOVERY FOR: {self.domain}")
        print("=" * 60)
        
        all_urls = []
        discovery_stats = {
            'domain': self.domain,
            'started_at': datetime.now().isoformat(),
            'methods_used': [],
            'total_urls_found': 0,
            'urls_by_source': {},
            'errors': []
        }
        
        # Method 1: Parse robots.txt for sitemaps
        try:
            print("\n🤖 METHOD 1: Robots.txt Analysis")
            sitemap_urls = self.parse_robots_txt()
            discovery_stats['methods_used'].append('robots_txt')
            
            # Parse each sitemap found in robots.txt
            for sitemap_url in sitemap_urls:
                sitemap_urls_data = self.parse_sitemap(sitemap_url)
                all_urls.extend(sitemap_urls_data)
            
        except Exception as e:
            error_msg = f"Robots.txt method failed: {e}"
            print(f"❌ {error_msg}")
            discovery_stats['errors'].append(error_msg)
        
        # Method 2: Try common sitemap locations
        try:
            print("\n📄 METHOD 2: Common Sitemap Locations")
            common_sitemaps = [
                f"{self.base_url}/sitemap.xml",
                f"{self.base_url}/sitemap_index.xml",
                f"{self.base_url}/sitemaps.xml"
            ]
            
            for sitemap_url in common_sitemaps:
                sitemap_urls_data = self.parse_sitemap(sitemap_url)
                if sitemap_urls_data:  # Only add if we found URLs
                    all_urls.extend(sitemap_urls_data)
                    break  # Stop after finding first working sitemap
            
            discovery_stats['methods_used'].append('sitemap_parsing')
            
        except Exception as e:
            error_msg = f"Sitemap parsing method failed: {e}"
            print(f"❌ {error_msg}")
            discovery_stats['errors'].append(error_msg)
        
        # Method 3: Crawl homepage and follow internal links
        try:
            print("\n🕷️  METHOD 3: Recursive Crawling")
            crawled_urls = self.discover_urls_recursive(self.base_url, max_crawl_depth)
            all_urls.extend(crawled_urls)
            discovery_stats['methods_used'].append('recursive_crawling')

        except Exception as e:
            error_msg = f"Recursive crawling method failed: {e}"
            print(f"❌ {error_msg}")
            discovery_stats['errors'].append(error_msg)
        
        # Remove duplicates and sort by priority
        unique_urls = {}
        for url_data in all_urls:
            url = url_data['url']
            if url not in unique_urls:
                unique_urls[url] = url_data
            else:
                # Merge data from multiple sources
                existing = unique_urls[url]
                existing['source'] = f"{existing.get('source', '')},{url_data.get('source', '')}"
        
        # Convert to list and sort by priority
        final_urls = list(unique_urls.values())
        final_urls.sort(key=lambda x: x.get('calculated_priority', 5), reverse=True)
        
        # Generate statistics
        discovery_stats['total_urls_found'] = len(final_urls)
        discovery_stats['completed_at'] = datetime.now().isoformat()
        
        # Count URLs by source
        for url_data in final_urls:
            source = url_data.get('source', 'unknown')
            discovery_stats['urls_by_source'][source] = discovery_stats['urls_by_source'].get(source, 0) + 1
        
        print(f"\n✅ URL DISCOVERY COMPLETED!")
        print(f"📊 Total URLs found: {len(final_urls)}")
        print(f"📋 Sources used: {', '.join(discovery_stats['methods_used'])}")
        
        return {
            'urls': final_urls,
            'stats': discovery_stats
        }
    
    def save_discovered_urls(self, discovery_result: Dict, output_file: str = None) -> str:
        """Save discovered URLs to JSON file."""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"discovered_urls_{self.domain}_{timestamp}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(discovery_result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"💾 URLs saved to: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ Failed to save URLs: {e}")
            return None

def main():
    """Test the URL discovery engine."""
    print("🔍 URL Discovery Engine - Phase 1 Testing")
    print()

    # Get domain from user
    domain = input("📝 Enter domain to discover URLs (e.g., itpyx.pk): ").strip()

    if not domain:
        print("❌ Please enter a valid domain!")
        return

    try:
        # Create discovery engine
        discovery = URLDiscoveryEngine(domain)

        # Discover all URLs
        result = discovery.discover_all_urls(max_crawl_depth=2)
        
        # Save results
        output_file = discovery.save_discovered_urls(result)
        
        # Display summary
        print("\n" + "=" * 60)
        print("🎉 URL DISCOVERY SUMMARY")
        print("=" * 60)
        
        stats = result['stats']
        print(f"🌐 Domain: {stats['domain']}")
        print(f"📊 Total URLs: {stats['total_urls_found']}")
        print(f"🔧 Methods: {', '.join(stats['methods_used'])}")
        print(f"📁 Output file: {output_file}")
        
        if stats['urls_by_source']:
            print(f"\n📋 URLs by source:")
            for source, count in stats['urls_by_source'].items():
                print(f"   • {source}: {count} URLs")
        
        if result['urls']:
            print(f"\n🔝 Top 10 URLs by priority:")
            for i, url_data in enumerate(result['urls'][:10], 1):
                priority = url_data.get('calculated_priority', 5)
                source = url_data.get('source', 'unknown')
                print(f"   {i}. [{priority}] {url_data['url']} ({source})")
        
        print(f"\n✅ Phase 1 completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\n👋 Discovery cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Discovery failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
