#!/usr/bin/env python3
"""
Enhanced Direct MCP Scraper
Uses direct subprocess communication with enhanced content extraction methods
"""

import asyncio
import json
import os
import subprocess
from datetime import datetime
from typing import Dict, Any, Optional

class EnhancedDirectScraper:
    """Enhanced scraper using direct MCP communication with robust content extraction."""
    
    def __init__(self):
        self.mcp_process = None
        self.request_id = 1
        
    async def start_mcp_server(self):
        """Start the MCP server."""
        print("🔧 Starting MCP server...")
        
        env = os.environ.copy()
        env.update({
            "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "true",
            "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/google-chrome-stable",
            "DISPLAY": ":99"
        })
        
        cmd = [
            "xvfb-run", "-a",
            "--server-args=-screen 0 1024x768x24 -ac +extension GLX +render -noreset",
            "npx", "-y", "@modelcontextprotocol/server-puppeteer"
        ]
        
        try:
            self.mcp_process = subprocess.Popen(
                cmd, env=env, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                stderr=subprocess.PIPE, text=True, bufsize=0
            )
            await asyncio.sleep(3)
            print("✅ MCP server started")
            return True
        except Exception as e:
            print(f"❌ Failed to start MCP server: {e}")
            return False
    
    async def send_mcp_request(self, method, params=None):
        """Send MCP request."""
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        self.request_id += 1
        
        try:
            request_json = json.dumps(request) + "\n"
            self.mcp_process.stdin.write(request_json)
            self.mcp_process.stdin.flush()
            
            response_line = await asyncio.wait_for(
                asyncio.to_thread(self.mcp_process.stdout.readline),
                timeout=30.0
            )
            
            if response_line.strip():
                return json.loads(response_line.strip())
            else:
                return {"error": "Empty response"}
        except Exception as e:
            return {"error": f"Communication error: {e}"}
    
    def extract_content_robust(self, response):
        """Robust content extraction handling all MCP response formats."""
        if "result" not in response:
            return None
            
        result = response["result"]
        
        # Handle Format A: Standard MCP format
        if isinstance(result, dict) and "content" in result:
            content_list = result["content"]
            if isinstance(content_list, list) and content_list:
                first_item = content_list[0]
                if isinstance(first_item, dict) and "text" in first_item:
                    text = first_item["text"]
                    
                    # Handle Format C: Execution wrapper
                    if "Execution result:" in text:
                        lines = text.split('\n')
                        for line in lines:
                            line = line.strip()
                            if (line and 
                                not line.startswith('Execution result:') and 
                                not line.startswith('Console output:') and
                                len(line) > 50):
                                # Remove quotes if present
                                if line.startswith('"') and line.endswith('"'):
                                    line = line[1:-1]
                                return line
                    
                    return text
        
        # Handle Format B: Direct string result
        elif isinstance(result, str):
            return result
        
        return None

    async def extract_content_with_fallbacks(self, url: str) -> Dict[str, Any]:
        """Extract content using multiple fallback methods."""
        print("📝 Extracting content with comprehensive fallback methods...")
        
        # Define multiple extraction methods with fixed JavaScript syntax
        extraction_methods = [
            {
                "name": "Simple innerText",
                "script": "document.body ? document.body.innerText : 'No body element'"
            },
            {
                "name": "Simple textContent",
                "script": "document.body ? document.body.textContent : 'No body element'"
            },
            {
                "name": "Document element text",
                "script": "document.documentElement ? document.documentElement.innerText : 'No document element'"
            },
            {
                "name": "Enhanced innerText with cleanup",
                "script": """(function() {
                    try {
                        if (!document.body) return 'No body element';
                        let content = document.body.innerText || '';
                        content = content.replace(/\\s+/g, ' ').trim();
                        return content || 'No content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            },
            {
                "name": "Enhanced textContent with cleanup",
                "script": """(function() {
                    try {
                        if (!document.body) return 'No body element';
                        let content = document.body.textContent || '';
                        content = content.replace(/\\s+/g, ' ').trim();
                        return content || 'No content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            },
            {
                "name": "Selective element extraction",
                "script": """(function() {
                    try {
                        const selectors = ['main', 'article', '.content', '#content', 'p', 'div', 'h1', 'h2', 'h3'];
                        let allText = [];

                        for (let i = 0; i < selectors.length; i++) {
                            const selector = selectors[i];
                            const elements = document.querySelectorAll(selector);
                            for (let j = 0; j < elements.length; j++) {
                                const el = elements[j];
                                const text = el.textContent ? el.textContent.trim() : '';
                                if (text && text.length > 10 && allText.indexOf(text) === -1) {
                                    allText.push(text);
                                }
                            }
                        }

                        const content = allText.join(' ').replace(/\\s+/g, ' ').trim();
                        return content || 'No content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            },
            {
                "name": "All elements text extraction",
                "script": """(function() {
                    try {
                        const elements = document.querySelectorAll('*');
                        let allText = [];

                        for (let i = 0; i < elements.length; i++) {
                            const el = elements[i];
                            const text = el.textContent ? el.textContent.trim() : '';
                            if (text && text.length > 10) {
                                allText.push(text);
                            }
                        }

                        const content = allText.join(' ').replace(/\\s+/g, ' ').trim();
                        return content || 'No substantial content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            }
        ]
        
        # Try each method until we get substantial content
        for method in extraction_methods:
            print(f"   Trying: {method['name']}")
            
            try:
                response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": method['script']}
                })
                
                content = self.extract_content_robust(response)
                
                if content and len(content) > 100 and not content.startswith('Error:'):
                    print(f"   ✅ Success with {method['name']}: {len(content)} characters")
                    return {"status": "success", "result": content, "method": method['name']}
                else:
                    content_preview = content[:100] if content else "None"
                    print(f"   ⚠️  {method['name']} returned insufficient content: {len(content) if content else 0} chars")
                    print(f"       Preview: '{content_preview}'")
                    
            except Exception as e:
                print(f"   ❌ {method['name']} exception: {e}")
                continue
        
        print("❌ All content extraction methods failed")
        return {"status": "error", "result": "All content extraction methods failed", "method": "none"}

    async def scrape_website_enhanced(self, url: str) -> Dict[str, Any]:
        """Enhanced website scraping with retry logic."""
        print(f"🕷️  ENHANCED DIRECT SCRAPING: {url}")
        print("=" * 60)
        
        max_retries = 3
        for attempt in range(max_retries):
            if attempt > 0:
                print(f"\n🔄 RETRY ATTEMPT {attempt + 1}/{max_retries}")
                await asyncio.sleep(2)
            
            try:
                # Initialize MCP session
                print("🔧 Initializing MCP session...")
                init_response = await self.send_mcp_request("initialize", {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {"roots": {"listChanged": False}, "sampling": {}},
                    "clientInfo": {"name": "enhanced-scraper", "version": "1.0.0"}
                })
                
                if "error" in init_response:
                    print(f"❌ Initialization failed: {init_response['error']}")
                    if attempt < max_retries - 1:
                        continue
                    return {"status": "error", "error": "MCP initialization failed"}
                
                print("✅ MCP session initialized")
                
                # Navigate
                print(f"🌐 Navigating to {url}...")
                nav_response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_navigate",
                    "arguments": {"url": url}
                })
                
                # Wait for page load with verification
                print("⏱️  Waiting for page to load...")
                await asyncio.sleep(8)  # Longer wait

                # Check page state with safer scripts
                print("🔍 Checking page state...")
                state_response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": "document.readyState"}
                })
                state = self.extract_content_robust(state_response)
                print(f"   Page state: {state}")

                # Check if body exists and has content
                body_check_response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": "document.body ? document.body.innerHTML.length : 0"}
                })
                body_length_str = self.extract_content_robust(body_check_response)
                print(f"   Body HTML length: {body_length_str}")

                # Additional wait if needed - with safer parsing
                try:
                    if body_length_str and body_length_str.isdigit() and int(body_length_str) < 1000:
                        print("   ⏳ Body seems small, waiting longer for dynamic content...")
                        await asyncio.sleep(5)
                except (ValueError, TypeError):
                    print("   ⏳ Could not parse body length, waiting for safety...")
                    await asyncio.sleep(3)
                
                # Take screenshot
                timestamp = datetime.now().strftime('%H%M%S')
                screenshot_name = f"enhanced_scrape_{timestamp}"
                print(f"📸 Taking screenshot: {screenshot_name}")
                await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_screenshot",
                    "arguments": {"name": screenshot_name}
                })
                
                # Extract content with fallbacks
                content_result = await self.extract_content_with_fallbacks(url)
                
                if content_result["status"] == "success":
                    content = content_result["result"]
                    content_length = len(content)
                    word_count = len(content.split())
                    
                    result = {
                        "url": url,
                        "status": "success",
                        "scraped_at": datetime.now().isoformat(),
                        "screenshot": screenshot_name,
                        "content": content,
                        "content_length": content_length,
                        "word_count": word_count,
                        "extraction_method": content_result["method"],
                        "attempt": attempt + 1
                    }
                    
                    print(f"\n📊 ENHANCED SCRAPING SUCCESS!")
                    print(f"✅ Status: SUCCESS")
                    print(f"📝 Content: {content_length} characters, {word_count} words")
                    print(f"🔧 Method: {content_result['method']}")
                    print(f"📸 Screenshot: {screenshot_name}")
                    print(f"🔄 Attempt: {attempt + 1}")
                    
                    print(f"\n📄 CONTENT PREVIEW (First 500 chars):")
                    print("-" * 50)
                    print(f"{content[:500]}...")
                    print("-" * 50)
                    
                    return result
                else:
                    print(f"⚠️  Content extraction failed on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        continue
                        
            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    continue
        
        return {
            "url": url,
            "status": "error",
            "error": "All retry attempts failed",
            "scraped_at": datetime.now().isoformat()
        }

    async def cleanup(self):
        """Clean up resources."""
        if self.mcp_process:
            self.mcp_process.terminate()
            try:
                await asyncio.wait_for(asyncio.to_thread(self.mcp_process.wait), timeout=5)
            except asyncio.TimeoutError:
                self.mcp_process.kill()

async def main():
    """Test the enhanced direct scraper."""
    print("🚀 Enhanced Direct MCP Scraper")
    print("Using direct subprocess communication with enhanced content extraction")
    print()
    
    scraper = EnhancedDirectScraper()
    
    try:
        if await scraper.start_mcp_server():
            result = await scraper.scrape_website_enhanced("https://itpyx.pk")
            
            if result and result.get('status') == 'success':
                # Save results
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"enhanced_scraping_results_{timestamp}.json"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False, default=str)
                
                print(f"\n💾 Results saved to: {output_file}")
                print("🎉 ENHANCED SCRAPING SUCCESSFUL!")
                return output_file
            else:
                print("❌ Enhanced scraping failed")
                return None
        else:
            print("❌ Failed to start MCP server")
            return None
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        await scraper.cleanup()

if __name__ == "__main__":
    result_file = asyncio.run(main())
    if result_file:
        print(f"\n🎯 CHECK THIS FILE: {result_file}")
    else:
        print("\n❌ No output file generated")
