#!/usr/bin/env python3
"""
🌐 SIMPLE WEB SCRAPER - User-Friendly Interface
Perfect for non-technical users who just want to scrape websites easily!
"""

import asyncio
import json
from datetime import datetime
from enhanced_direct_scraper import EnhancedDirectScraper

class SimpleWebScraper:
    """User-friendly wrapper for the enhanced scraper."""
    
    def __init__(self):
        self.scraper = EnhancedDirectScraper()
        
    async def scrape_website_simple(self, url: str):
        """Simple interface for scraping any website."""
        print("🌐 SIMPLE WEB SCRAPER")
        print("=" * 50)
        print(f"📍 Target: {url}")
        print("⏳ Starting scraping process...")
        print()
        
        try:
            # Start the scraper
            if await self.scraper.start_mcp_server():
                print("✅ Browser started successfully!")
                
                # Scrape the website
                result = await self.scraper.scrape_website_enhanced(url)
                
                # Show user-friendly results
                await self._display_results(result)
                
                # Save results
                filename = await self._save_results(result)
                
                return result, filename
            else:
                print("❌ Failed to start browser. Please check your setup.")
                return None, None
                
        except Exception as e:
            print(f"❌ Error occurred: {e}")
            return None, None
        finally:
            await self.scraper.cleanup()
    
    async def _display_results(self, result):
        """Display results in a user-friendly format."""
        print("\n" + "=" * 60)
        print("📊 SCRAPING RESULTS")
        print("=" * 60)
        
        if result and result.get('status') == 'success':
            content_length = result.get('content_length', 0)
            word_count = result.get('word_count', 0)
            
            print("✅ SUCCESS! Website content extracted successfully!")
            print()
            print(f"📝 Content Statistics:")
            print(f"   • Characters: {content_length:,}")
            print(f"   • Words: {word_count:,}")
            print(f"   • Method: {result.get('extraction_method', 'Unknown')}")
            print()
            
            # Show content preview
            content = result.get('content', '')
            if content:
                print("📄 CONTENT PREVIEW (First 300 characters):")
                print("-" * 50)
                preview = content[:300].replace('\n', ' ').strip()
                print(f'"{preview}..."')
                print("-" * 50)
            
            print(f"📸 Screenshot saved: {result.get('screenshot', 'N/A')}")
            
        else:
            print("❌ FAILED to extract content")
            error = result.get('error', 'Unknown error') if result else 'No result returned'
            print(f"💡 Reason: {error}")
            print()
            print("🔧 Troubleshooting Tips:")
            print("   • Check if the website URL is correct")
            print("   • Ensure you have internet connection")
            print("   • Some websites may block automated access")
            print("   • Try again in a few minutes")
    
    async def _save_results(self, result):
        """Save results to a JSON file."""
        if not result:
            return None
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"scraped_content_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n💾 Results saved to: {filename}")
            print("   You can open this file to see all extracted data!")
            return filename
            
        except Exception as e:
            print(f"⚠️  Could not save results: {e}")
            return None

async def main():
    """Main function - Simple interface for users."""
    print("🎉 Welcome to the Simple Web Scraper!")
    print("This tool can extract complete content from any website.")
    print("Perfect for beginners - we'll guide you through everything!")
    print()

    # Get URL from user with extra guidance
    print("💡 Tips:")
    print("   • You can enter any website URL (like google.com or news.ycombinator.com)")
    print("   • We'll add 'https://' automatically if needed")
    print("   • The process takes about 30-60 seconds")
    print()

    while True:
        url = input("🌐 Enter the website URL to scrape: ").strip()

        if not url:
            print("❌ Please enter a valid URL!")
            print("💡 Example: google.com or https://example.com")
            continue

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            print(f"📝 Using URL: {url}")

        # Simple confirmation
        print(f"✅ Ready to scrape: {url}")
        break
    
    print()
    print("🚀 Starting scraping process...")
    print("⏳ This may take 30-60 seconds depending on the website...")
    print()
    
    # Create scraper and run
    scraper = SimpleWebScraper()
    result, filename = await scraper.scrape_website_simple(url)
    
    print("\n" + "=" * 60)
    
    if result and result.get('status') == 'success':
        print("🎉 SCRAPING COMPLETED SUCCESSFULLY!")
        print()
        print("📁 What you got:")
        print(f"   • Complete website content extracted")
        print(f"   • {result.get('content_length', 0):,} characters of text")
        print(f"   • Screenshot of the page")
        print(f"   • Results saved in: {filename}")
        print()
        print("💡 You can now:")
        print("   • Open the JSON file to see all data")
        print("   • Use the extracted content for analysis")
        print("   • Run the scraper on other websites")
        
    else:
        print("❌ SCRAPING FAILED")
        print()
        print("🔧 What to try:")
        print("   • Check the website URL")
        print("   • Try a different website")
        print("   • Run the scraper again")
        print("   • Contact support if issues persist")
    
    print("\n🙏 Thank you for using Simple Web Scraper!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Scraping cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please try running the scraper again.")
