#!/bin/bash
# Quick Start Script for Browser MCP Server
# Run this to start web scraping immediately

echo "🚀 Starting Browser MCP Server for Web Scraping"
echo "================================================"

# Set up virtual display
echo "🖥️  Setting up virtual display..."
export DISPLAY=:99
if ! pgrep -f "Xvfb :99" > /dev/null; then
    Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
    echo "✅ Virtual display started"
else
    echo "✅ Virtual display already running"
fi

# Activate Python environment
echo "🐍 Activating Python environment..."
source venv/bin/activate

# Set environment variables
echo "🔧 Setting environment variables..."
export GEMINI_API_KEY=AIzaSyDyTy1lucjTonWlfSwHcBi1aC65W7482Go
export PYTHONPATH="$PWD/src:$PYTHONPATH"
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

echo ""
echo "✅ Browser MCP Server Ready!"
echo ""
echo "🎯 You can now:"
echo "   1. Run Python scripts with web scraping"
echo "   2. Use all browser automation tools"
echo "   3. Navigate, screenshot, and extract content"
echo ""
echo "📝 Example usage:"
echo "   python your_scraping_script.py"
echo ""
echo "🔧 Available tools:"
echo "   - puppeteer_navigate: Navigate to URLs"
echo "   - puppeteer_screenshot: Capture screenshots"
echo "   - puppeteer_evaluate: Execute JavaScript"
echo "   - puppeteer_click: Click elements"
echo "   - puppeteer_fill: Fill forms"
echo ""
echo "🎉 Happy scraping!"