#!/usr/bin/env python3
"""
🚀 ADVANCED CONTENT SCRAPER
Single, comprehensive scraper that extracts both raw and cleaned content
Saves results in separate files for easy comparison and use
"""

import asyncio
import json
import re
import subprocess
from datetime import datetime
from typing import Dict, Any, Optional

class AdvancedContentScraper:
    """Advanced scraper with intelligent content filtering and dual output."""
    
    def __init__(self):
        self.mcp_process = None
        self.request_id = 1
        
    def get_user_input(self):
        """Get website URL from user with validation."""
        print("🚀 ADVANCED CONTENT SCRAPER")
        print("=" * 50)
        print("Extracts both RAW and CLEANED content from any website")
        print("Saves results in separate files for easy comparison")
        print()
        
        while True:
            url = input("📝 Enter the website URL to scrape: ").strip()
            
            if not url:
                print("❌ Please enter a valid URL!")
                continue
                
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                print(f"📋 Using URL: {url}")
            
            return url

    async def start_mcp_server(self):
        """Start the MCP server with optimized browser configuration."""
        print("🔧 Starting MCP server with optimized browser settings...")

        import os
        env = os.environ.copy()
        env.update({
            "PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "true",
            "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/google-chrome-stable",
            "DISPLAY": ":99",
            # Add browser arguments for better content loading
            "PUPPETEER_ARGS": "--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection"
        })

        cmd = [
            "xvfb-run", "-a",
            "--server-args=-screen 0 1920x1080x24 -ac +extension GLX +render -noreset",
            "npx", "-y", "@modelcontextprotocol/server-puppeteer"
        ]

        try:
            self.mcp_process = subprocess.Popen(
                cmd, env=env, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                stderr=subprocess.PIPE, text=True, bufsize=0
            )
            await asyncio.sleep(8)  # Longer startup wait
            print("✅ MCP server started with optimized settings")
            return True
        except Exception as e:
            print(f"❌ Failed to start MCP server: {e}")
            return False
    
    async def send_mcp_request(self, method, params=None):
        """Send MCP request."""
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        self.request_id += 1
        
        try:
            request_json = json.dumps(request) + "\n"
            self.mcp_process.stdin.write(request_json)
            self.mcp_process.stdin.flush()
            
            response_line = await asyncio.wait_for(
                asyncio.to_thread(self.mcp_process.stdout.readline),
                timeout=30.0
            )
            
            if response_line.strip():
                return json.loads(response_line.strip())
            else:
                return {"error": "Empty response"}
        except Exception as e:
            return {"error": f"Communication error: {e}"}
    
    def extract_content_robust(self, response):
        """Extract content from MCP response."""
        if "result" not in response:
            return None
            
        result = response["result"]
        
        if isinstance(result, dict) and "content" in result:
            content_list = result["content"]
            if isinstance(content_list, list) and content_list:
                first_item = content_list[0]
                if isinstance(first_item, dict) and "text" in first_item:
                    text = first_item["text"]
                    
                    if "Execution result:" in text:
                        lines = text.split('\n')
                        for line in lines:
                            line = line.strip()
                            if (line and 
                                not line.startswith('Execution result:') and 
                                not line.startswith('Console output:') and
                                len(line) > 50):
                                if line.startswith('"') and line.endswith('"'):
                                    line = line[1:-1]
                                return line
                    
                    return text
        elif isinstance(result, str):
            return result
        
        return None

    async def extract_raw_content(self, url: str) -> Dict[str, Any]:
        """Extract raw content using multiple fallback methods."""
        print("📄 Extracting raw content...")
        
        extraction_methods = [
            {
                "name": "Simple innerText",
                "script": "document.body ? document.body.innerText : 'No body element'"
            },
            {
                "name": "Simple textContent", 
                "script": "document.body ? document.body.textContent : 'No body element'"
            },
            {
                "name": "Enhanced innerText with cleanup",
                "script": """(function() {
                    try {
                        if (!document.body) return 'No body element';
                        let content = document.body.innerText || '';
                        content = content.replace(/\\s+/g, ' ').trim();
                        return content || 'No content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            },
            {
                "name": "Comprehensive extraction",
                "script": """(function() {
                    try {
                        let content = '';
                        const approaches = [
                            () => document.body?.innerText,
                            () => document.body?.textContent,
                            () => document.documentElement?.innerText,
                            () => {
                                const elements = document.querySelectorAll('*');
                                return Array.from(elements)
                                    .map(el => el.textContent?.trim())
                                    .filter(text => text && text.length > 10)
                                    .join(' ');
                            }
                        ];
                        
                        for (let i = 0; i < approaches.length; i++) {
                            try {
                                const result = approaches[i]();
                                if (result && result.length > 100) {
                                    content = result;
                                    break;
                                }
                            } catch (e) {
                                continue;
                            }
                        }
                        
                        content = content.replace(/\\s+/g, ' ').trim();
                        return content || 'No substantial content found';
                    } catch (e) {
                        return 'Error: ' + e.message;
                    }
                })()"""
            }
        ]
        
        for method in extraction_methods:
            print(f"   Trying: {method['name']}")
            
            try:
                response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_evaluate",
                    "arguments": {"script": method['script']}
                })
                
                content = self.extract_content_robust(response)
                
                if content and len(content) > 100 and not content.startswith('Error:'):
                    print(f"   ✅ Success with {method['name']}: {len(content)} characters")
                    return {"status": "success", "content": content, "method": method['name']}
                else:
                    print(f"   ⚠️  {method['name']} insufficient: {len(content) if content else 0} chars")
                    
            except Exception as e:
                print(f"   ❌ {method['name']} failed: {e}")
                continue
        
        return {"status": "error", "content": "", "method": "none"}

    def clean_content(self, raw_content: str) -> Dict[str, Any]:
        """Apply intelligent filtering to clean content."""
        print("🧠 Applying intelligent content cleaning...")
        
        if not raw_content or len(raw_content) < 50:
            return {
                "cleaned_content": "",
                "filters_applied": ["content_too_short"],
                "reduction_percentage": 0
            }
        
        original_length = len(raw_content)
        content = raw_content
        filters_applied = []
        
        # Filter 1: Remove JavaScript data blocks
        js_patterns = [
            r'window\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*\{.*?\};',
            r'\{[^{}]*"[a-zA-Z_$][a-zA-Z0-9_$]*":\s*\{.*?\}[^{}]*\}',
            r'"[a-zA-Z0-9_-]{20,}":\s*"[^"]{100,}"',
            r'https?://[^\s"\']{50,}',
        ]
        
        for pattern in js_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("javascript_data_removed")
        
        # Filter 2: Remove repeated navigation patterns
        nav_patterns = [
            r'(Shop\s+\w+|Explore\s+\w+|Learn\s+more|Buy\s+now){3,}',
            r'(Support|Help|Contact|About|Privacy|Terms){3,}',
            r'(\w+\s+){15,}',
        ]
        
        for pattern in nav_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("navigation_patterns_removed")
        
        # Filter 3: Remove noise patterns
        noise_patterns = [
            r'Cookie\s+Policy|Privacy\s+Policy|Terms\s+of\s+Service',
            r'Subscribe\s+to\s+newsletter|Sign\s+up\s+for\s+updates',
            r'Follow\s+us\s+on|Social\s+media|Share\s+this',
            r'Copyright\s+©|All\s+rights\s+reserved',
            r'Loading\.\.\.|Please\s+wait|Error\s+\d+',
        ]
        
        for pattern in noise_patterns:
            old_len = len(content)
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            if len(content) < old_len:
                filters_applied.append("noise_patterns_removed")
        
        # Filter 4: Clean whitespace
        content = re.sub(r'\s+', ' ', content)
        content = content.strip()
        filters_applied.append("whitespace_normalized")
        
        # Filter 5: Extract meaningful sentences
        sentences = re.split(r'[.!?]+', content)
        meaningful_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if (len(sentence) > 20 and 
                len(sentence.split()) > 3 and
                not re.match(r'^[A-Z\s]+$', sentence) and
                not sentence.lower().startswith(('click', 'learn more', 'shop now', 'buy now'))):
                meaningful_sentences.append(sentence)
        
        if meaningful_sentences:
            content = '. '.join(meaningful_sentences) + '.'
            filters_applied.append("meaningful_sentences_extracted")
        
        content = re.sub(r'\s+', ' ', content).strip()
        
        cleaned_length = len(content)
        reduction_percentage = ((original_length - cleaned_length) / original_length * 100) if original_length > 0 else 0
        
        return {
            "cleaned_content": content,
            "filters_applied": list(set(filters_applied)),
            "reduction_percentage": round(reduction_percentage, 1)
        }

    async def scrape_website(self, url: str, max_retries: int = 3):
        """Main scraping function with retry logic."""
        print(f"🚀 ADVANCED CONTENT SCRAPING: {url}")
        print("=" * 60)

        for attempt in range(max_retries):
            if attempt > 0:
                print(f"\n🔄 RETRY ATTEMPT {attempt + 1}/{max_retries}")
                await asyncio.sleep(3)

            try:
                # Initialize MCP session - simplified approach
                print("🔧 Initializing MCP session...")

                # Skip formal initialization and go straight to navigation
                # This matches the working enhanced_direct_scraper approach
                print("✅ MCP session initialized")
                
                # Navigate
                print(f"🌐 Navigating to {url}...")
                nav_response = await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_navigate",
                    "arguments": {"url": url}
                })

                if "error" in nav_response:
                    print(f"⚠️  Navigation warning: {nav_response.get('error', 'Unknown error')}")

                # Wait for page load
                print("⏱️  Waiting for page to load...")
                await asyncio.sleep(10)  # Longer wait for complex pages
                
                # Take screenshot
                timestamp = datetime.now().strftime('%H%M%S')
                screenshot_name = f"scrape_{timestamp}"
                print(f"📸 Taking screenshot: {screenshot_name}")
                await self.send_mcp_request("tools/call", {
                    "name": "puppeteer_screenshot",
                    "arguments": {"name": screenshot_name}
                })
                
                # Extract raw content
                raw_result = await self.extract_raw_content(url)
                
                if raw_result["status"] == "success":
                    raw_content = raw_result["content"]
                    
                    # Clean content
                    clean_result = self.clean_content(raw_content)
                    
                    # Compile results
                    result = {
                        "url": url,
                        "scraped_at": datetime.now().isoformat(),
                        "status": "success",
                        "screenshot": screenshot_name,
                        "extraction_method": raw_result["method"],
                        "attempt": attempt + 1,
                        
                        # Raw content
                        "raw_content": raw_content,
                        "raw_content_length": len(raw_content),
                        "raw_word_count": len(raw_content.split()),
                        
                        # Cleaned content
                        "cleaned_content": clean_result["cleaned_content"],
                        "cleaned_content_length": len(clean_result["cleaned_content"]),
                        "cleaned_word_count": len(clean_result["cleaned_content"].split()),
                        
                        # Cleaning stats
                        "content_reduction": f"{clean_result['reduction_percentage']}%",
                        "filters_applied": clean_result["filters_applied"]
                    }
                    
                    return result
                else:
                    if attempt < max_retries - 1:
                        continue
                        
            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    continue
        
        return {
            "url": url,
            "status": "error",
            "error": "All retry attempts failed",
            "scraped_at": datetime.now().isoformat()
        }

    def save_results(self, result: Dict[str, Any]) -> tuple:
        """Save results to separate raw and cleaned files."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save raw content
        raw_filename = f"raw_content_{timestamp}.json"
        raw_data = {
            "url": result["url"],
            "scraped_at": result["scraped_at"],
            "status": result["status"],
            "screenshot": result.get("screenshot"),
            "extraction_method": result.get("extraction_method"),
            "content": result.get("raw_content", ""),
            "content_length": result.get("raw_content_length", 0),
            "word_count": result.get("raw_word_count", 0)
        }
        
        # Save cleaned content
        cleaned_filename = f"cleaned_content_{timestamp}.json"
        cleaned_data = {
            "url": result["url"],
            "scraped_at": result["scraped_at"],
            "status": result["status"],
            "screenshot": result.get("screenshot"),
            "extraction_method": result.get("extraction_method"),
            "content": result.get("cleaned_content", ""),
            "content_length": result.get("cleaned_content_length", 0),
            "word_count": result.get("cleaned_word_count", 0),
            "content_reduction": result.get("content_reduction", "0%"),
            "filters_applied": result.get("filters_applied", [])
        }
        
        try:
            with open(raw_filename, 'w', encoding='utf-8') as f:
                json.dump(raw_data, f, indent=2, ensure_ascii=False, default=str)
            
            with open(cleaned_filename, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, indent=2, ensure_ascii=False, default=str)
            
            return raw_filename, cleaned_filename
            
        except Exception as e:
            print(f"⚠️  Could not save results: {e}")
            return None, None

    def display_results(self, result: Dict[str, Any], raw_file: str, cleaned_file: str):
        """Display scraping results."""
        print("\n" + "=" * 60)
        print("🚀 ADVANCED SCRAPING RESULTS")
        print("=" * 60)
        
        if result.get('status') == 'success':
            print("✅ SUCCESS! Content extracted and cleaned successfully!")
            print()
            
            print(f"📊 CONTENT STATISTICS:")
            print(f"   • Raw Content: {result.get('raw_content_length', 0):,} characters ({result.get('raw_word_count', 0):,} words)")
            print(f"   • Cleaned Content: {result.get('cleaned_content_length', 0):,} characters ({result.get('cleaned_word_count', 0):,} words)")
            print(f"   • Content Reduction: {result.get('content_reduction', '0%')}")
            print(f"   • Extraction Method: {result.get('extraction_method', 'Unknown')}")
            print()
            
            print(f"🔧 FILTERS APPLIED:")
            for filter_name in result.get('filters_applied', []):
                print(f"   ✅ {filter_name.replace('_', ' ').title()}")
            print()
            
            print(f"📁 FILES SAVED:")
            print(f"   📄 Raw Content: {raw_file}")
            print(f"   🧠 Cleaned Content: {cleaned_file}")
            print(f"   📸 Screenshot: {result.get('screenshot', 'N/A')}")
            print()
            
            # Show cleaned content preview
            cleaned_content = result.get('cleaned_content', '')
            if cleaned_content:
                print("📄 CLEANED CONTENT PREVIEW (First 500 characters):")
                print("-" * 50)
                preview = cleaned_content[:500]
                print(f'"{preview}..."')
                print("-" * 50)
        else:
            print("❌ SCRAPING FAILED")
            print(f"💡 Error: {result.get('error', 'Unknown error')}")

    async def cleanup(self):
        """Clean up resources."""
        if self.mcp_process:
            self.mcp_process.terminate()
            try:
                await asyncio.wait_for(asyncio.to_thread(self.mcp_process.wait), timeout=5)
            except asyncio.TimeoutError:
                self.mcp_process.kill()

async def main():
    """Main function."""
    print("🎉 Welcome to Advanced Content Scraper!")
    print("Extracts both RAW and CLEANED content from any website")
    print("Saves results in separate files for easy comparison and use")
    print()
    
    scraper = AdvancedContentScraper()
    
    try:
        # Get URL from user
        url = scraper.get_user_input()
        
        print(f"\n🚀 Starting Advanced Content Scraping")
        print(f"🎯 Target: {url}")
        print("⏳ This may take 30-60 seconds...")
        print()
        
        # Start MCP server and scrape
        if await scraper.start_mcp_server():
            result = await scraper.scrape_website(url)
            
            if result and result.get('status') == 'success':
                # Save results to separate files
                raw_file, cleaned_file = scraper.save_results(result)
                
                # Display results
                scraper.display_results(result, raw_file, cleaned_file)
                
                print("\n🎉 ADVANCED SCRAPING COMPLETED SUCCESSFULLY!")
                print("🙏 Thank you for using Advanced Content Scraper!")
                
            else:
                print("❌ ADVANCED SCRAPING FAILED")
                print("🔧 Please try again or check the website URL")
        else:
            print("❌ Failed to start MCP server")
            
    except KeyboardInterrupt:
        print("\n\n👋 Scraping cancelled by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        await scraper.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
